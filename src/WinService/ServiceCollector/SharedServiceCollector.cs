using EventHandlers.Contracts;
using EventHandlers.Services;
using EventHandlers.Services.Account;
using Microsoft.Extensions.DependencyInjection;

namespace WinService.ServiceCollector
{
    public static class SharedServiceCollector
    {
        public static void AddSharedServices(this IServiceCollection serviceCollection)
        {
            serviceCollection.AddSingleton<ISharedService, SharedService>();
            serviceCollection.AddSingleton<ILoanApplicationSharedService, LoanApplicationSharedService>();
        }
    }
}
