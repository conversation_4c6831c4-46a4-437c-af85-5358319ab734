using Domain.Commands.ConsentForm;
using FluentValidation;

namespace Validators.ConsentForm;

public class CreateConsentFormValidator : AbstractValidator<CreateConsentFormCommand>
{
    public CreateConsentFormValidator()
    {
        RuleFor(command => command.MemberItemId)
            .NotNull()
            .NotEmpty()
            .WithMessage("Member ItemId is required");

        RuleFor(command => command.FundAccountItemId)
            .NotNull()
            .NotEmpty()
            .WithMessage("Fund Account ItemId is required");

        RuleFor(command => command.ReceiveAccountCode)
            .NotNull()
            .NotEmpty()
            .WithMessage("Receive Account Code is required");

        RuleFor(command => command.ReceiveAccountItemId)
            .NotNull()
            .NotEmpty()
            .WithMessage("Receive Account ItemId is required");
    }

}