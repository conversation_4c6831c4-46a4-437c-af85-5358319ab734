using EventHandlers.Contracts;
using FinMongoDbRepositories;
using Infrastructure.Constants;
using Infrastructure.Contracts;
using Infrastructure.Events.Account;
using Infrastructure.Events.Register;
using Infrastructure.Extensions;
using Microsoft.Extensions.Logging;
using Microsoft.OpenApi.Extensions;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Product;
using SeliseBlocks.Entities.PrimaryEntities.Enums;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace EventHandlers.Services.Account
{
    public class LoanDisbursementPostProcessingEventService : ILoanDisbursementPostProcessingEventService
    {
        private readonly ILogger<LoanDisbursementPostProcessingEventService> _logger;
        private readonly IFinMongoDbRepository _finMongoDbRepository;
        private readonly ISharedService _sharedService;
        private readonly ISecurityContextProvider _securityContextProvider;
        private readonly IServiceClient _serviceClient;
        private readonly ITransactionClientService _transactionClientService;

        public LoanDisbursementPostProcessingEventService(
            ILogger<LoanDisbursementPostProcessingEventService> logger,
            IFinMongoDbRepository repository,
            ISharedService sharedService,
            ISecurityContextProvider securityContextProvider,
            IServiceClient serviceClient,
            ITransactionClientService transactionClientService)
        {
            _logger = logger;
            _finMongoDbRepository = repository;
            _sharedService = sharedService;
            _securityContextProvider = securityContextProvider;
            _serviceClient = serviceClient;
            _transactionClientService = transactionClientService;
        }

        public async Task LoanDisbursementPostProcessingAsync(LoanDisbursementPostProcessingEvent command)
        {
            _logger.LogInformation("Starting LoanDisbursementPostProcessingAsync for loan application Id: {LoanApplicationItemId}", command.LoanApplicationItemId);

            var securityContext = _securityContextProvider.GetSecurityContext();
            var currentEmployee = await _finMongoDbRepository.FindOneAsync<BuroEmployee>(e => e.UserItemId == securityContext.UserId);

            await HandleLoanDisbursementSubmissionAsync(command, currentEmployee, securityContext);

            _logger.LogInformation("Finished LoanDisbursementPostProcessingAsync for loan application Id: {LoanApplicationItemId}", command.LoanApplicationItemId);
        }

        private async Task HandleLoanDisbursementSubmissionAsync(LoanDisbursementPostProcessingEvent command, BuroEmployee currentEmployee, SecurityContext securityContext)
        {
            var loanApplication = await _finMongoDbRepository.FindOneAsync<BuroLoanApplication>(a => a.ItemId == command.LoanApplicationItemId);

            var loandDisbursement = await _finMongoDbRepository.FindOneAsync<BuroLoanApplicationDisbursement>(x => x.LoanApplicationItemId == command.LoanApplicationItemId);

            var productLine = await _finMongoDbRepository.FindOneAsync<LoanProductLine>(x => x.ItemId == loanApplication.ProductLineItemId);

            var loanInitiator = await _finMongoDbRepository.FindOneAsync<BuroEmployee>(e => e.ItemId == loanApplication.Initiator.ItemId);

            switch (command.Status)
            {
                case EnumLoanApplicationDisbursementStatus.NegotiationSubmited:
                    await HandleNegotiationSubmitionAsync(command, currentEmployee, loanInitiator, securityContext);
                    break;
                case EnumLoanApplicationDisbursementStatus.LoanScheduleSubmited:
                    await HandleLoanScheduleSubmitionAsync(command, currentEmployee, loanInitiator, loandDisbursement, productLine, securityContext);
                    break;
                case EnumLoanApplicationDisbursementStatus.CustomerSecurityFundCollected:
                    await HandleCustomerSecurityFundCollectionAsync(command, currentEmployee, loanInitiator, securityContext, loanApplication);
                    break;
                case EnumLoanApplicationDisbursementStatus.DisbursementAcknowledged:
                    await HandleDisbursementAcknowledgementAsync(command, currentEmployee, loanInitiator, securityContext, loanApplication);
                    break;
                default:
                    break;
            }

            // NOSONAR maintain disbursment history
        }

        private async Task HandleNegotiationSubmitionAsync(
            LoanDisbursementPostProcessingEvent command,
            BuroEmployee currentEmployee,
            BuroEmployee loanInitiator,
            SecurityContext securityContext)
        {
            await SendNotificationAsync(command, currentEmployee, loanInitiator, securityContext);
            // NOSONAR may need to generate loan schedule
        }

        private async Task HandleLoanScheduleSubmitionAsync(
            LoanDisbursementPostProcessingEvent command,
            BuroEmployee currentEmployee,
            BuroEmployee loanInitiator,
            BuroLoanApplicationDisbursement applicationDisbursement,
            LoanProductLine loanProductLine,
            SecurityContext securityContext)
        {
            var disbursementDate = applicationDisbursement.CreateDate;
            await SendNotificationAsync(command, currentEmployee, loanInitiator, securityContext);

            var negotiationInfo = applicationDisbursement.NegotiationInformation;
            var firstInstallmentDate = negotiationInfo.FirstInstallmentDate!.Value;
            var maxGracePeriod = loanProductLine.MaxGracePeriodInDays;

            var allowedMaxDate = disbursementDate.AddDays(maxGracePeriod);

            if (firstInstallmentDate.Date > allowedMaxDate.Date)
            {
                var exceededDays = (firstInstallmentDate - allowedMaxDate).Days;

                var interestRate = loanProductLine.InterestRate / 100.0;
                var disbursedAmount = applicationDisbursement.DisbursementAcknowledgementDetails.Amount;
                var lateFeePerDay = (disbursedAmount * interestRate) / BuroProductConstants.DaysInYear;
                var lateFee = lateFeePerDay * exceededDays;

                var updates = new Dictionary<string, object>
                {
                    { nameof(BuroLoanApplicationDisbursement.LastUpdateDate), DateTime.UtcNow },
                    { nameof(BuroLoanApplicationDisbursement.LastUpdatedBy), securityContext.UserId },
                    { $"{nameof(BuroLoanApplicationDisbursement.NegotiationInformation)}.LateFeeAmount", lateFee }
                };

                await _finMongoDbRepository.UpdateOneAsync<BuroLoanApplicationDisbursement>(x => x.ItemId == applicationDisbursement.ItemId, updates);
            }
        }

        private async Task HandleCustomerSecurityFundCollectionAsync(
            LoanDisbursementPostProcessingEvent command,
            BuroEmployee currentEmployee,
            BuroEmployee loanInitiator,
            SecurityContext securityContext,
            BuroLoanApplication loanApplication)
        {
            await UpdateLoanMicroInsuranceAsync(command, currentEmployee);
            await SaveLoanApplicationHistoryAsync(loanApplication, currentEmployee, loanApplication.Status);
            await SendNotificationAsync(command, currentEmployee, loanInitiator, securityContext);
        }

        private async Task HandleDisbursementAcknowledgementAsync(
            LoanDisbursementPostProcessingEvent command,
            BuroEmployee currentEmployee,
            BuroEmployee loanInitiator,
            SecurityContext securityContext,
            BuroLoanApplication loanApplication)
        {
            var loanAccount = await CreateLoanAccountAsync(command, loanApplication);

            await SaveLoanAccountHistoryAsync(loanAccount, currentEmployee);
            await UpdateLoanLoanApplicationStatusAsync(command, currentEmployee, EnumProductApplicationStatus.Disbursed);
            await SaveLoanApplicationHistoryAsync(loanApplication, currentEmployee, EnumProductApplicationStatus.Disbursed);
            await UpdateLoanCountofAMemberAsync(currentEmployee.UserItemId, loanApplication.MemberItemId);
            await UpdateMemberGuarantorFlagAsync(currentEmployee.UserItemId, loanApplication.Guarantors);

            SendToCreateScheduleForLoanAccount(loanApplication.ItemId);
            await SendToCreateChequeRequestForTheLoan(loanApplication, loanAccount);

            await SendNotificationAsync(command, currentEmployee, loanInitiator, securityContext);
        }

        private async Task SendToCreateChequeRequestForTheLoan(BuroLoanApplication loanApplication, BuroLoanAccount loanAccount)
        {
            var disbusement = await GetLoanApplicationDisbursementAsync(string.Empty, loanApplication.ItemId);

            var payload = new ChequeRegisterPostProcessingEvent
            {
                BranchItemId = loanApplication.BranchItemId,
                MemberItemId = loanApplication.MemberItemId,
                MemberName = loanApplication.MemberName,
                MemberId = loanApplication.MemberSequenceNumber,
                ChequeNumber = disbusement?.NegotiationInformation?.ChequeInformation?.ChequeNumber ?? default,
                BankName = disbusement?.NegotiationInformation?.ChequeInformation.BankName ?? default,
                RoutingNumber = disbusement?.NegotiationInformation?.ChequeInformation.RoutingNumber ?? default,
                MICRLine = disbusement?.NegotiationInformation?.ChequeInformation.MicrLineNumber ?? default,
                ChequeBeneficiaryName = disbusement?.NegotiationInformation?.ChequeInformation.InfavorOf ?? default,
                Amount = disbusement?.NegotiationInformation?.ChequeInformation.Amount ?? default,
                ChequeFileStorageItemId = disbusement?.NegotiationInformation?.SecurityChequeFileItemId ?? default,
                DepositAccountItemId = loanAccount.ItemId,
                DepositAccountCode = loanAccount.AccountCode,
                DepositAccountNumber = loanAccount.AccountSequenceNumber,
                IsEligibleReturn = false,
            };

            _serviceClient.SendToQueue<bool>(QueueNames.BuroCommandQueue, payload);

            _logger.LogInformation("CS account schedule creation initiated for account Id: {ItemId}", loanAccount.ItemId);
        }

        private async Task UpdateMemberGuarantorFlagAsync(string userItemId, List<Guarantor> guarantors)
        {
            var buroGurantorItemIds = guarantors.Where(g => g.IsGuarantorExistingBuroMember && !string.IsNullOrWhiteSpace(g.MemberItemId))
                .Select(g => g.MemberItemId)
                .ToList();

            var properties = new Dictionary<string, object>
            {
                {nameof(BuroMember.LastUpdateDate), DateTime.UtcNow },
                {nameof(BuroMember.LastUpdatedBy), userItemId },
                {nameof(BuroMember.IsAGuarantor), true },
            };

            await _finMongoDbRepository.UpdateManyAsync<BuroMember>(m => buroGurantorItemIds.Contains(m.ItemId), properties);
        }

        private async Task UpdateLoanCountofAMemberAsync(string userItemId, string memberItemId)
        {
            var currentLoanCount = (await _finMongoDbRepository.FindWithProjectionAsync<BuroMember, int>(
                dataFilters: m => m.ItemId == memberItemId,
                projectionExpression: m => m.NumberOfLoanAccounts))
                .FirstOrDefault();

            var properties = new Dictionary<string, object>
            {
                {nameof(BuroMember.LastUpdateDate), DateTime.UtcNow },
                {nameof(BuroMember.LastUpdatedBy), userItemId },
                {nameof(BuroMember.NumberOfLoanAccounts), currentLoanCount + 1 },
            };

            await _finMongoDbRepository.UpdateOneAsync<BuroMember>(m => m.ItemId == memberItemId, properties);
        }

        private async Task UpdateLoanLoanApplicationStatusAsync(
            LoanDisbursementPostProcessingEvent command,
            BuroEmployee currentEmployee,
            EnumProductApplicationStatus status)
        {
            var properties = new Dictionary<string, object>
            {
                {nameof(BuroLoanApplication.LastUpdateDate), DateTime.UtcNow },
                {nameof(BuroLoanApplication.LastUpdatedBy), currentEmployee.UserItemId },
                {nameof(BuroLoanApplication.Status), status},
            };

            await _finMongoDbRepository.UpdateOneAsync<BuroLoanApplication>(d => d.ItemId == command.LoanApplicationItemId, properties);
        }

        private async Task SaveLoanApplicationHistoryAsync(BuroLoanApplication loanApplication, BuroEmployee employee, EnumProductApplicationStatus status)
        {
            _logger.LogInformation("Saving Loan application history for loan ID: {ItemId}", loanApplication.ItemId);

            var history = new BuroProductApplicationHistory
            {
                ApplicationItemId = loanApplication.ItemId,
                ActionByEmployeeItemId = employee.ItemId,
                ActionByEmployeePin = employee.EmployeePin,
                ActionByEmployeeName = employee.EmployeeName,
                ActionByEmployeeDesginationItemId = employee.DesignationItemId,
                ActionByEmployeeDesginationTitle = employee.DesignationTitle,
                ActionByEmployeeOfficeItemId = employee.CurrentOfficeItemId,
                ActionByEmployeeOfficeName = employee.CurrentOfficeTitle,
                ActionByEmployeeOfficeCode = employee.CurrentOfficeCode,
                Status = status,
                ProductType = EnumProductType.Loan
            };

            history.AddEntityBasicInfo();

            await _finMongoDbRepository.InsertOneAsync(history);
        }

        private async Task<BuroLoanAccount> CreateLoanAccountAsync(LoanDisbursementPostProcessingEvent command, BuroLoanApplication loanApplication)
        {
            _logger.LogInformation("Starting CreateLoanAccountAsync for application Id: {AccountItemId}", command.LoanApplicationItemId);
            var loanDisbursement = await GetLoanApplicationDisbursementAsync(command.LoanApplicationDisbursementItemId, command.LoanApplicationItemId);
            var accountCode = _sharedService.GenerateAbbreviation(nameof(EnumProductType.Loan));
            var sequenceNumber = await _sharedService.GetSequenceNumberAsync(BuroConstants.BuroLoanAccountIdentifier, accountCode);
            var isPrimaryLine = await AssociateProductLineIsAPrimaryLineAsync(loanApplication);

            var account = new BuroLoanAccount
            {
                ItemId = loanApplication.ProposedLoanAccountItemId,
                AccountCode = accountCode,
                AccountSequenceNumber = sequenceNumber,
                IsPrimaryLine = isPrimaryLine,
                AccountStatus = EnumProductAccountStatus.Ongoing,
                OpeningBranchItemId = loanApplication.BranchItemId,
                OpeningBranchName = loanApplication.BranchName,
                CurrentBranchItemId = loanApplication.BranchItemId,
                CurrentBranchName = loanApplication.BranchName,
                OpeningDate = DateTime.UtcNow,
                MemberItemId = loanApplication.MemberItemId,
                PersonItemId = loanApplication.PersonItemId,
                CenterItemId = loanApplication.CenterItemId,
                CenterName = loanApplication.CenterName,
                ProductLineItemId = loanApplication.ProductLineItemId,
                ProductLineName = loanApplication.ProductLineName,
                InterestRate = loanApplication.InterestRate,
                Balance = loanApplication.FinalLoanAmount.GetValueOrDefault(),
                ProgramOrganizerEmployeeItemId = loanApplication.ProgramOrganizerEmployeeItemId,
                ProgramOrganizerEmployeeName = loanApplication.ProgramOrganizerEmployeeName,
                LoanAmount = loanApplication.FinalLoanAmount.GetValueOrDefault(),
                LoanApplicationItemId = loanApplication.ItemId,
                TenureDetails = loanApplication.TenureDetails,
                PaymentInterval = loanApplication.PaymentInterval,
                FirstInstallmentDate = loanDisbursement.NegotiationInformation?.FirstInstallmentDate,
                LoanType = EnumLoanType.MultipleInstallment, // NOSONAR Need Discussion
                LoanDisbursementDate = loanDisbursement.CreateDate,
                PlanedLoanEndDate = loanDisbursement.LoanSchedules.LastOrDefault()?.PaymentDate,
                DisbursedAmount = loanDisbursement.DisbursementAcknowledgementDetails.Amount,
                TotalNumberOfInstallment = loanApplication.TotalNumberOfInstallment,
                InstallmentAmount = 0, // NOSONAR Need Discussion
                TotalNumberOfRemainingInstallment = loanApplication.TotalNumberOfInstallment,
                TotalLoanDurationMonth = CalculateTotalLoanDurationInMonth(loanApplication.TenureDetails),
                LoanServiceCharge = loanDisbursement.FinalMicroInsuranceFee + loanDisbursement.LoanApplicationFee,
                TransactionMedium = loanDisbursement.DisbursementAcknowledgementDetails?.DisbursementPaymentOption ?? default,
                IsMicroEnterprise = true, // NOSONAR Need Discussion
                HasMemberWelfareFundCoverage = true,
                HasInsuranceCoverage = true, // NOSONAR Need Discussion
                LoanSectorItemId = loanApplication.LoanSectorItemId,
                LoanSectorName = loanApplication.LoanSectorName,
                LoanSectorCode = loanApplication.LoanSectorCode,
                MemberName = loanApplication.MemberName,
                MemberSequenceNumber = loanApplication.MemberSequenceNumber,
                MemberBranchOfficeItemId = loanApplication.BranchItemId,
                MemberBranchOfficeName = loanApplication.BranchName,
                LateFeeAmount = loanDisbursement.NegotiationInformation.LateFeeAmount ?? 0
            };

            account.AddEntityBasicInfo();

            var ressult = await _transactionClientService.CreateAccountAsync(new Infrastructure.Models.Transaction.CreateAccountCommand
            {
                AccountNumber = account.AccountSequenceNumber,
                AccountHolderNumber = account.MemberSequenceNumber,
                AccountName = EnumProductType.Loan.GetDisplayName(),
                InitialBalance = loanApplication.FinalLoanAmount.GetValueOrDefault(),
                AccountType = TransactionServiceConstrants.AccountTypeConstants.Loan,
            });

            if (!ressult.IsSuccess())
            {
                _logger.LogError("Failed to create account in transaction service for member Id: {MemberItemId}", account.MemberItemId);

                throw new InvalidOperationException("Failed to create account in transaction service.");
            }

            await _finMongoDbRepository.InsertOneAsync(account);

            return account;
        }

        private async Task<bool> AssociateProductLineIsAPrimaryLineAsync(BuroLoanApplication loanApplication)
        {
            var isPrimaryLines = await _finMongoDbRepository.FindWithProjectionAsync<LoanProductLine, bool>(
                l => l.ItemId == loanApplication.ProductLineItemId,
                l => l.PrimaryLine);

            return isPrimaryLines.FirstOrDefault();
        }

        private static int CalculateTotalLoanDurationInMonth(LoanLineTenure tenureDetails)
        {
            var value = tenureDetails.Duration.Value;
            var totalLoanDuratinInDays = tenureDetails.Duration.Unit switch
            {
                EnumTenureUnit.Week => value * BuroProductConstants.DaysInWeek,
                EnumTenureUnit.Month => value * BuroProductConstants.DaysInMonth,
                EnumTenureUnit.Year => value * BuroProductConstants.DaysInYear,
                _ => default
            };

            var totalLoanDurationInMonths = (double)totalLoanDuratinInDays / BuroProductConstants.DaysInMonth;

            return (int)Math.Ceiling(totalLoanDurationInMonths);
        }

        private async Task<BuroLoanApplicationDisbursement> GetLoanApplicationDisbursementAsync(string loanApplicationDisbursementItemId, string loanApplicationItemId)
        {
            return await _finMongoDbRepository.FindOneAsync<BuroLoanApplicationDisbursement>(
                d => d.ItemId == loanApplicationDisbursementItemId || d.LoanApplicationItemId == loanApplicationItemId)
                ?? throw new InvalidOperationException(string.Format(BuroErrorMessageKeys.RequiredDataNotFound, nameof(BuroLoanApplicationDisbursement)));
        }

        private async Task SaveLoanAccountHistoryAsync(BuroLoanAccount account, BuroEmployee currentEmployee)
        {
            _logger.LogInformation("Starting SaveLoanAccountHistoryAsync for account Id: {ItemId}", account.ItemId);

            var history = new BuroProductAccountHistory
            {
                AccountItemId = account.ItemId,
                ActionByEmployeeItemId = currentEmployee.ItemId,
                ActionByEmployeePin = currentEmployee.EmployeePin,
                ActionByEmployeeName = currentEmployee.EmployeeName,
                ActionByEmployeeDesginationItemId = currentEmployee.DesignationItemId,
                ActionByEmployeeDesginationTitle = currentEmployee.DesignationTitle,
                ActionByEmployeeOfficeItemId = currentEmployee.CurrentOfficeItemId,
                ActionByEmployeeOfficeName = currentEmployee.CurrentOfficeTitle,
                ActionByEmployeeOfficeCode = currentEmployee.CurrentOfficeCode,
                ProductType = EnumProductType.Loan,
                Status = account.AccountStatus,
                MetaData = new List<MetaData>
                {
                    new()
                    {
                        Key = nameof(account.MemberItemId),
                        Value = account.MemberItemId
                    },
                    new()
                    {
                        Key = nameof(account.ProductLineItemId),
                        Value = account.ProductLineItemId
                    }
                }
            };

            history.AddEntityBasicInfo();

            await _finMongoDbRepository.InsertOneAsync(history);
        }

        private void SendToCreateScheduleForLoanAccount(string applicationItemId)
        {
            var payload = new LoanAccountScheduleGenerationEvent
            {
                LoanApplicationItemId = applicationItemId
            };

            _serviceClient.SendToQueue<bool>(QueueNames.BuroCommandQueue, payload);

            _logger.LogInformation("CS account schedule creation initiated for application Id: {ApplicationItemId}", applicationItemId);
        }

        private async Task UpdateLoanMicroInsuranceAsync(LoanDisbursementPostProcessingEvent command, BuroEmployee currentEmployee)
        {
            var properties = new Dictionary<string, object>
            {
                {nameof(BuroLoanApplication.LastUpdateDate), DateTime.UtcNow },
                {nameof(BuroLoanApplication.LastUpdatedBy), currentEmployee.UserItemId },
                {nameof(BuroLoanApplication.FinalMicroInsuranceFee), command.FinalMicroInsuranceFee},
            };

            await _finMongoDbRepository.UpdateOneAsync<BuroLoanApplication>(d => d.ItemId == command.LoanApplicationItemId, properties);
        }

        private async Task SendNotificationAsync(
            LoanDisbursementPostProcessingEvent command,
            BuroEmployee sender,
            BuroEmployee receiver,
            SecurityContext securityContext)
        {
            _logger.LogInformation("Starting SendNotificationAsync");

            var responseKey = "Loan.Disbursement." + Enum.GetName(typeof(EnumLoanApplicationDisbursementStatus), command.Status);

            var notificationPayload = new
            {
                command.LoanApplicationItemId,
                AssignedAt = DateTime.UtcNow,
                AssignedBy = securityContext.DisplayName,
                RequestedByEmployeeName = sender.EmployeeName,
                RequestedByEmployeePin = sender.EmployeePin,
                RequestedByEmployeeDesignation = sender.DesignationTitle,
                RequestedByEmployeeOfficeName = sender.CurrentOfficeTitle,
                RequestedByEmployeeOfficeCode = sender.CurrentOfficeCode
            };

            await _sharedService.NotifyUserAsync(
                responseKey,
                notificationPayload,
                command.LoanApplicationItemId,
                receiver.UserItemId);

            _logger.LogInformation("Notification sent to employee with Id: {ItemId}", receiver.ItemId);
        }
    }
}
