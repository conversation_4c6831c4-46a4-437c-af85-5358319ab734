using EventHandlers.Contracts;
using FinMongoDbRepositories;
using Infrastructure.Constants;
using Infrastructure.Contracts;
using Infrastructure.Enums;
using Infrastructure.Events.Account;
using Infrastructure.Extensions;
using Infrastructure.Models;
using Microsoft.Extensions.Logging;
using MongoDB.Driver;
using Newtonsoft.Json;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Product;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace EventHandlers.Services.Account
{
    public class LoanAccountScheduleGenerationService : ILoanAccountScheduleGenerationService
    {
        private readonly ILogger<LoanAccountScheduleGenerationService> _logger;
        private readonly IRepository _repository;
        private readonly IFinMongoDbRepository _finMongoDbRepository;
        private readonly IWorkingDayService _workingDayService;
        private readonly ISecurityContextProvider _securityContextProvider;
        private readonly INotificationServiceClient _notificationServiceClient;

        public LoanAccountScheduleGenerationService(
            ILogger<LoanAccountScheduleGenerationService> logger,
            IRepository repository,
            IFinMongoDbRepository finMongoDbRepository,
            IWorkingDayService workingDayService,
            ISecurityContextProvider securityContextProvider,
            INotificationServiceClient notificationServiceClient)
        {
            _logger = logger;
            _repository = repository;
            _finMongoDbRepository = finMongoDbRepository;
            _workingDayService = workingDayService;
            _securityContextProvider = securityContextProvider;
            _notificationServiceClient = notificationServiceClient;
        }

        public async Task GenerateLoanAccountSchedule(LoanAccountScheduleGenerationEvent command)
        {
            _logger.LogInformation("Starting Loan schedule generation for ApplicationItemId: {ApplicationItemId}", command.LoanApplicationItemId);

            var (loanApplication, loanDisbursement, member, center) = await GetRequiredEntities(command.LoanApplicationItemId);
            if (loanApplication == null || loanDisbursement == null || member == null || center == null) return;

            var firstInstallmentDate = loanDisbursement.NegotiationInformation.FirstInstallmentDate?.Date;
            if (!firstInstallmentDate.HasValue)
                throw new InvalidOperationException($"Loan application '{loanApplication.ItemId}' is missing FirstInstallmentDate.");

            var scheduleExists = await _repository.ExistsAsync<BuroMemberSchedule>(
                x => x.AccountItemId == loanApplication.ProposedLoanAccountItemId &&
                     x.ActualInstallmentNumber == 1 &&
                     x.ActualPaymentDate == firstInstallmentDate.Value);

            if (scheduleExists)
            {
                await NotifyScheduleGeneration(loanApplication.ItemId, loanApplication.ProposedLoanAccountItemId, command.SubscriptionId);
                return;
            }

            await DeleteExistingSchedulesBeforeGeneration(loanDisbursement.ProposedLoanAccountItemId);

            var holidayHashes = await _workingDayService.GetHolidaySetAsync(DateTime.UtcNow, center.ItemId, member.BranchOfficeItemId, member.ItemId);

            var weekend = (await _finMongoDbRepository.FindAsync<BuroWeekend>(_ => true)).FirstOrDefault();

            var scheduleTemplate = await GetLoanProductScheduleTemplateAsync(loanApplication);
            if (scheduleTemplate == null) return;

            var (schedules, excessAmount) = await GenerateSchedules(loanApplication, loanDisbursement, scheduleTemplate, holidayHashes, weekend);
            if (!schedules.Any())
            {
                _logger.LogWarning("No schedules generated for ApplicationItemId: {ApplicationItemId}", loanApplication.ItemId);
                return;
            }

            await _repository.SaveAsync(schedules);
            await UpdateExcessAmountAsync(loanApplication.ItemId, loanApplication.ProposedLoanAccountItemId, excessAmount);
            await NotifyScheduleGeneration(loanApplication.ItemId, loanApplication.ProposedLoanAccountItemId, command.SubscriptionId);
        }

        private async Task DeleteExistingSchedulesBeforeGeneration(string proposedLoanAccountItemId)
        {
            _logger.LogInformation("Deleting existing loan schedules for Account ItemId {AccountId}", proposedLoanAccountItemId);

            await _repository.DeleteAsync<BuroMemberSchedule>(x => x.AccountItemId == proposedLoanAccountItemId);
        }

        private async Task<(BuroLoanApplication, BuroLoanApplicationDisbursement, BuroMember, BuroCenter)> GetRequiredEntities(string applicationItemId)
        {
            var loanApplication = await _repository.GetItemAsync<BuroLoanApplication>(x => x.ItemId == applicationItemId);
            var loanDisbursement = await _repository.GetItemAsync<BuroLoanApplicationDisbursement>(x => x.LoanApplicationItemId == applicationItemId);
            var member = loanApplication != null ? await _repository.GetItemAsync<BuroMember>(x => x.ItemId == loanApplication.MemberItemId) : null;
            var center = member != null ? await _repository.GetItemAsync<BuroCenter>(x => x.ItemId == member.CenterItemId) : null;

            if (loanApplication == null) _logger.LogWarning("Loan application not found for ItemId: {AccountItemId}", applicationItemId);
            if (loanDisbursement == null) _logger.LogWarning("Loan disbursement not found for ItemId: {AccountItemId}", applicationItemId);
            if (member == null) _logger.LogWarning("Member not found for ItemId: {MemberItemId}", loanApplication?.MemberItemId);
            if (center == null) _logger.LogWarning("Center not found for ItemId: {CenterItemId}", member?.CenterItemId);

            return (loanApplication, loanDisbursement, member, center);
        }

        private async Task<LoanProductLineSchedule> GetLoanProductScheduleTemplateAsync(BuroLoanApplication loanApplication)
        {
            var tenure = loanApplication.TenureDetails.Duration;
            var schedule = await _repository.GetItemAsync<LoanProductLineSchedule>(
                x => x.Tenure.Value == tenure.Value &&
                     x.Tenure.Unit == tenure.Unit &&
                     x.LoanProductLineItemId == loanApplication.ProductLineItemId);

            if (schedule == null)
                _logger.LogWarning("LoanProductLineSchedule not found for AccountItemId: {AccountItemId}", loanApplication.ItemId);

            return schedule;
        }

        private async Task<(List<BuroMemberSchedule>, double)> GenerateSchedules(BuroLoanApplication loanApplication, BuroLoanApplicationDisbursement disbursement, LoanProductLineSchedule template, HashSet<DateTime> holidayHashes, BuroWeekend weekend)
        {
            var schedules = new List<BuroMemberSchedule>();
            var firstDate = disbursement.NegotiationInformation.FirstInstallmentDate!.Value;
            var currentDate = firstDate;
            var scale = (loanApplication.FinalLoanAmount ?? 0) / template.ExamplePrincipal;
            double excessAmount = scale * template.ExcessAmount;

            int totalInstallment = template.NumberOfInstallments;

            for (int i = 0; i < totalInstallment; i++)
            {
                int instNum = i + 1;
                var breakdown = template.InstallmentBreakdown.FirstOrDefault(b => instNum >= b.StartInstallment && instNum <= b.EndInstallment);

                double principal = (breakdown?.PrincipalAmount ?? 0) * scale;
                double service = (breakdown?.ServiceCharge ?? 0) * scale;
                double raw = principal + service;
                double rounded = Math.Ceiling(raw);
                excessAmount += (rounded - raw);

                var schedule = CreateSchedule(loanApplication, instNum, totalInstallment, currentDate, rounded);
                schedule.AddEntityBasicInfo();
                schedules.Add(schedule);

                currentDate = loanApplication.PaymentInterval switch
                {
                    EnumPaymentInterval.Weekly => await GetNextWeeklyScheduleDate(currentDate, holidayHashes, weekend),
                    EnumPaymentInterval.Monthly => await GetNextMonthlyScheduleDate(firstDate, instNum, holidayHashes, weekend),
                    _ => throw new NotSupportedException($"Unsupported PaymentInterval: {loanApplication.PaymentInterval}")
                };
            }

            return (schedules, excessAmount);
        }

        private static BuroMemberSchedule CreateSchedule(BuroLoanApplication app, int installment, int totalInstallment, DateTime date, double amount) =>
            new()
            {
                AccountItemId = app.ProposedLoanAccountItemId,
                AccountCode = "LOAN",
                MemberItemId = app.MemberItemId,
                OriginalInstallmentNumber = installment,
                ActualInstallmentNumber = installment,
                TotalInstallmentCount = totalInstallment,
                PaymentInterval = app.PaymentInterval,
                OriginalPaymentDate = date,
                ActualPaymentDate = date,
                PayableAmount = amount,
                PaymentStatus = PaymentStatus.Pending,
                IsAutoDebited = false,
                ProductType = EnumProductType.Loan
            };

        private async Task UpdateExcessAmountAsync(string accountId, string applicationItemId, double excessAmount)
        {
            if (excessAmount <= 0) return;

            var context = _securityContextProvider.GetSecurityContext();
            var updateAccount = new Dictionary<string, object>
            {
                [nameof(BuroLoanAccount.LastUpdatedBy)] = context.UserId,
                [nameof(BuroLoanAccount.LastUpdateDate)] = DateTime.UtcNow,
                [nameof(BuroLoanAccount.ExcessAmount)] = excessAmount
            };

            var updateApplication = new Dictionary<string, object>
            {
                [nameof(BuroLoanApplication.LastUpdatedBy)] = context.UserId,
                [nameof(BuroLoanApplication.LastUpdateDate)] = DateTime.UtcNow,
                [nameof(BuroLoanApplication.ExcessAmount)] = excessAmount
            };

            await Task.WhenAll(
                _repository.UpdateAsync<BuroLoanAccount>(x => x.ItemId == accountId, updateAccount),
                _repository.UpdateAsync<BuroLoanApplication>(x => x.ItemId == applicationItemId, updateApplication)
            );

        }

        private async Task NotifyScheduleGeneration(string applicationItemId, string accountIdItemId, string subscriptionId)
        {
            _logger.LogInformation("NotifyScheduleGeneration for ApplicationItemId: {ApplicationItemId} -> START", applicationItemId);

            var payload = new { ApplicationItemId = applicationItemId, AccountItemId = accountIdItemId };

            var notificationModel = new NotifierPayloadWithResponse
            {
                ResponseKey = "Loan.ScheduleGeneration",
                NotificationType = NotificationReceiverTypes.FilterSpecificReceiverType,
                ResponseValue = JsonConvert.SerializeObject(payload),
                SubscriptionFilters = new()
                {
                    new SubscriptionFilter { Context = "Loan.ScheduleGeneration", ActionName = "ScheduleGeneration", Value = subscriptionId }
                }
            };

            await _notificationServiceClient.NotifyAsync(notificationModel);

            _logger.LogInformation("NotifyScheduleGeneration for ApplicationItemId: {ApplicationItemId} -> END", applicationItemId);
        }

        public async Task<DateTime> GetNextWeeklyScheduleDate(DateTime current, HashSet<DateTime> holidayHashes, BuroWeekend weekend)
        {
            var date = current.AddDays(BuroProductConstants.DaysInWeek);
            while (await _workingDayService.IsWeekendAsync(date, weekend) ||
                await _workingDayService.IsHolidayAsync(date, holidayHashes))
            {
                _logger.LogWarning("Next scheduled date {Date} is a holiday. Skipping...", date);
                date = date.AddDays(BuroProductConstants.DaysInWeek);
            }
            return date;
        }

        public async Task<DateTime> GetNextMonthlyScheduleDate(DateTime first, int number, HashSet<DateTime> holidayHashes, BuroWeekend weekend)
        {
            var date = first.AddMonths(number);
            while (await _workingDayService.IsWeekendAsync(date, weekend) ||
                await _workingDayService.IsHolidayAsync(date, holidayHashes))
            {
                _logger.LogWarning("Next scheduled date {Date} is a holiday. Skipping...", date);
                date = date.AddDays(-1);
            }
            return date;
        }
    }
}
