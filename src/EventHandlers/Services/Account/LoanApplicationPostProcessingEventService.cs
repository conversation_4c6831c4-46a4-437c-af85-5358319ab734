using EventHandlers.Contracts;
using Infrastructure.Constants;
using Infrastructure.Enums;
using Infrastructure.Events.Account;
using Infrastructure.Extensions;
using Microsoft.Extensions.Logging;
using MongoDB.Driver;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Product;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace EventHandlers.Services.Account
{
    public class LoanApplicationPostProcessingEventService : ILoanApplicationPostProcessingEventService
    {
        private readonly ILogger<LoanApplicationPostProcessingEventService> _logger;
        private readonly IRepository _repository;
        private readonly ISharedService _sharedService;
        private readonly ILoanApplicationSharedService _loanApplicationSharedService;
        private readonly ISecurityContextProvider _securityContextProvider;

        public LoanApplicationPostProcessingEventService(
            ILogger<LoanApplicationPostProcessingEventService> logger,
            IRepository repository,
            ISharedService commonService,
            ILoanApplicationSharedService loanApplicationSharedService,
            ISecurityContextProvider securityContextProvider)
        {
            _logger = logger;
            _repository = repository;
            _sharedService = commonService;
            _loanApplicationSharedService = loanApplicationSharedService;
            _securityContextProvider = securityContextProvider;
        }

        public async Task LoanApplicationPostProcessingAsync(LoanApplicationPostProcessingEvent command)
        {
            _logger.LogInformation("Starting LoanApplicationPostProcessingAsync for loan application Id: {LoanApplicationItemId}", command.LoanApplicationItemId);

            var securityContext = _securityContextProvider.GetSecurityContext();
            var currentEmployee = await _repository.GetItemAsync<BuroEmployee>(e => e.UserItemId == securityContext.UserId);

            await HandleLoanSubmissionAsync(command, currentEmployee, securityContext);

            _logger.LogInformation("Finished LoanApplicationPostProcessingAsync for loan application Id: {LoanApplicationItemId}", command.LoanApplicationItemId);
        }

        private async Task HandleLoanSubmissionAsync(LoanApplicationPostProcessingEvent command, BuroEmployee currentEmployee, SecurityContext securityContext)
        {
            switch (command.Status)
            {
                case EnumProductApplicationStatus.Pending:
                    await HandleNewLoanApplicationSubmissionAsync(command, currentEmployee, securityContext);
                    break;
                case EnumProductApplicationStatus.ApprovalOngoing:
                case EnumProductApplicationStatus.Approved:
                    await HandleLoanApplicationApprovalAsync(command, currentEmployee, securityContext);
                    break;
                case EnumProductApplicationStatus.Rejected:
                case EnumProductApplicationStatus.Discarded:
                    await HandleLoanApplicationRejectionAsync(command, currentEmployee);
                    break;
                case EnumProductApplicationStatus.Correction:
                    await HandleLoanApplicationCorrectionAsync(command, currentEmployee);
                    break;
                default:
                    break;
            }
        }

        private async Task HandleNewLoanApplicationSubmissionAsync(
            LoanApplicationPostProcessingEvent command,
            BuroEmployee currentEmployee,
            SecurityContext securityContext)
        {
            _logger.LogInformation("Inside HandleNewLoanApplicationSubmissionAsync with loan application Id: {LoanApplicationItemId}", command.LoanApplicationItemId);

            var loanApplication = await GetLoanApplicationAsync(command.LoanApplicationItemId);

            var reviewer = await GetEmployeeAsync(loanApplication.Reviewer?.ItemId);

            if (reviewer != null)
            {
                await SendNotificationToBranchAccountantAsync(command, currentEmployee, reviewer, securityContext);
            }
        }

        private async Task<BuroEmployee> GetEmployeeAsync(string employeeItemId)
        {
            if (string.IsNullOrEmpty(employeeItemId))
            {
                return null;
            }

            return await _repository.GetItemAsync<BuroEmployee>(e => e.ItemId == employeeItemId);
        }

        private async Task<BuroLoanApplication> GetLoanApplicationAsync(string loanApplicationItemId)
        {
            return await _repository.GetItemAsync<BuroLoanApplication>(l => l.ItemId == loanApplicationItemId)
                ?? throw new InvalidOperationException(string.Format(BuroErrorMessageKeys.RequiredDataNotFound, "LOANAPPLICATION"));
        }

        private async Task SendNotificationToBranchAccountantAsync(
            LoanApplicationPostProcessingEvent command,
            BuroEmployee initiator,
            BuroEmployee reviewer,
            SecurityContext securityContext)
        {
            _logger.LogInformation("Starting SendNotificationToBranchAccountantAsync");

            var responseKey = BuroLoanApplicationNotificationKeys.GetNotificationKeyByDesignationCode(reviewer.DesignationCode, EnumLoanActions.Invited);

            var notificationPayload = new
            {
                command.LoanApplicationItemId,
                AssignedAt = DateTime.UtcNow,
                AssignedBy = securityContext.DisplayName,
                RequestedByEmployeeName = initiator.EmployeeName,
                RequestedByEmployeePin = initiator.EmployeePin,
                RequestedByEmployeeDesignation = initiator.DesignationTitle,
                RequestedByEmployeeOfficeName = initiator.CurrentOfficeTitle,
                RequestedByEmployeeOfficeCode = initiator.CurrentOfficeCode
            };

            await _sharedService.NotifyUserAsync(
                responseKey,
                notificationPayload,
                command.LoanApplicationItemId,
                reviewer.UserItemId);

            _logger.LogInformation("Notification sent to employee with Id: {ItemId}", reviewer.ItemId);
        }

        private async Task HandleLoanApplicationApprovalAsync(
            LoanApplicationPostProcessingEvent command,
            BuroEmployee currentEmployee,
            SecurityContext securityContext)
        {
            var loanApplication = await _repository.GetItemAsync<BuroLoanApplication>(l => l.ItemId == command.LoanApplicationItemId);
            var productLine = await _repository.GetItemAsync<LoanProductLine>(l => l.ItemId == loanApplication.ProductLineItemId);

            var nextApprover = await _loanApplicationSharedService.GetNextLoanApproverAsync(loanApplication.FinalLoanAmount.Value, productLine.TemplateItemId, currentEmployee);

            if (nextApprover != null)
            {
                await SendApprovalAsync(command, currentEmployee, nextApprover, securityContext);
            }
            else
            {
                await UpdateLoanApplicationAsync(command, currentEmployee.UserItemId, EnumProductApplicationStatus.Approved);
                await SaveLoanApplicationHistoryAsync(command, currentEmployee, EnumProductApplicationStatus.Approved);
            }
        }

        private async Task SaveLoanApplicationHistoryAsync(LoanApplicationPostProcessingEvent command, BuroEmployee employee, EnumProductApplicationStatus status)
        {
            _logger.LogInformation("Saving Loan application history for loan ID: {LoanApplicationItemId}", command.LoanApplicationItemId);

            var history = new BuroProductApplicationHistory
            {
                ApplicationItemId = command.LoanApplicationItemId,
                ActionByEmployeeItemId = employee.ItemId,
                ActionByEmployeePin = employee.EmployeePin,
                ActionByEmployeeName = employee.EmployeeName,
                ActionByEmployeeDesginationItemId = employee.DesignationItemId,
                ActionByEmployeeDesginationTitle = employee.DesignationTitle,
                ActionByEmployeeOfficeItemId = employee.CurrentOfficeItemId,
                ActionByEmployeeOfficeName = employee.CurrentOfficeTitle,
                ActionByEmployeeOfficeCode = employee.CurrentOfficeCode,
                Status = status,
                ProductType = EnumProductType.Loan
            };

            history.AddEntityBasicInfo();

            await _repository.SaveAsync(history);
        }

        private async Task SendApprovalAsync(
            LoanApplicationPostProcessingEvent command,
            BuroEmployee initiator,
            BuroEmployee approver,
            SecurityContext securityContext)
        {
            _logger.LogInformation("Starting SendApprovalAsync");

            var responseKey = BuroLoanApplicationNotificationKeys.GetNotificationKeyByDesignationCode(initiator.DesignationCode, EnumLoanActions.Approved);
            var approvalItemId = await CreateApprovalAsync(command, initiator, approver, securityContext.UserId);

            var notificationPayload = new
            {
                command.LoanApplicationItemId,
                AssignedAt = DateTime.UtcNow,
                AssignedBy = securityContext.DisplayName,
                ApprovalItemId = approvalItemId,
                RequestedByEmployeeName = initiator.EmployeeName,
                RequestedByEmployeePin = initiator.EmployeePin,
                RequestedByEmployeeDesignation = initiator.DesignationTitle,
                RequestedByEmployeeOfficeName = initiator.CurrentOfficeTitle,
                RequestedByEmployeeOfficeCode = initiator.CurrentOfficeCode
            };

            await _sharedService.NotifyUserAsync(
                responseKey,
                notificationPayload,
                command.LoanApplicationItemId,
                approver.UserItemId);
        }

        private async Task UpdateLoanApplicationAsync(LoanApplicationPostProcessingEvent command, string userItemId, EnumProductApplicationStatus status)
        {
            var properties = new Dictionary<string, object>
            {
                {nameof(BuroLoanApplication.LastUpdateDate), DateTime.UtcNow },
                {nameof(BuroLoanApplication.LastUpdatedBy), userItemId },
                {nameof(BuroLoanApplication.Status), status }
            };

            if (!string.IsNullOrWhiteSpace(command.Remarks))
            {
                properties.Add(nameof(BuroLoanApplication.Remarks), command.Remarks);
            }

            await _repository.UpdateAsync<BuroLoanApplication>(l => l.ItemId == command.LoanApplicationItemId, properties);
        }

        private async Task<string> CreateApprovalAsync(
            LoanApplicationPostProcessingEvent command,
            BuroEmployee initiator,
            BuroEmployee approver,
            string currentUserId)
        {
            _logger.LogInformation("Starting CreateApprovalAsync");

            var approval = new BuroApproval
            {
                ItemId = Guid.NewGuid().ToString(),
                Tags = new[] { Tags.IsALoanApplicationApproval },
                CreatedBy = currentUserId,
                LastUpdatedBy = currentUserId,
                RelatedEntityItemId = command.LoanApplicationItemId,
                RelatedEntityName = nameof(BuroLoanApplication),
                RequestedFromPersonItemId = initiator.PersonItemId,
                RequestedFromEmployeeItemId = initiator.ItemId,
                RequestedFromEmployeePIN = initiator.EmployeePin,
                RequestedFromEmployeeName = initiator.EmployeeName,
                ActionedByPersonItemId = approver.PersonItemId,
                ActionedByEmployeeItemId = approver.ItemId,
                ActionedByEmployeePIN = approver.EmployeePin,
                ActionedByEmployeeName = approver.EmployeeName,
                Status = EnumApprovalStatusType.Pending,
                Category = EnumApprovalCategoryType.LoanApplicationApproval,
                ActionedByEmployeeDesignation = approver.DesignationTitle,
                MetaData = new List<MetaData>
                {
                    new() { Key = "LoanApplicationItemId", Value = command.LoanApplicationItemId },
                },
                IdsAllowedToRead = new[] { currentUserId, initiator.UserItemId },
                IdsAllowedToUpdate = new[] { currentUserId, initiator.UserItemId },
                IdsAllowedToDelete = new[] { currentUserId },
                IdsAllowedToWrite = new[] { currentUserId }
            };

            approval.AddEntityBasicInfo();

            await _repository.SaveAsync(approval);

            _logger.LogInformation("Loan application approval created with approval Id: {ItemId}", approval.ItemId);

            return approval.ItemId;
        }


        private async Task HandleLoanApplicationCorrectionAsync(LoanApplicationPostProcessingEvent command, BuroEmployee currentEmployee)
        {
            var loanApplicationInformation = _repository.GetItems<BuroLoanApplication>(l => l.ItemId == command.LoanApplicationItemId)
                .Select(l => new
                {
                    l.MemberItemId,
                    l.Initiator
                })
                .FirstOrDefault();

            if (loanApplicationInformation == null)
            {
                return;
            }

            var loanApplicationHistory = _repository.GetItems<BuroProductApplicationHistory>(
                a => a.ApplicationItemId == command.LoanApplicationItemId
                  && a.ActionByEmployeeItemId != currentEmployee.ItemId
                  && a.ProductType == EnumProductType.Loan
                  && (a.Status == EnumProductApplicationStatus.Pending
                  || a.Status == EnumProductApplicationStatus.Approved
                  || a.Status == EnumProductApplicationStatus.ApprovalOngoing));

            var newHistory = new List<BuroProductApplicationHistory>();

            foreach (var item in loanApplicationHistory)
            {
                var history = new BuroProductApplicationHistory
                {
                    ApplicationItemId = command.LoanApplicationItemId,
                    ActionByEmployeeItemId = item.ActionByEmployeeItemId,
                    ActionByEmployeePin = item.ActionByEmployeePin,
                    ActionByEmployeeName = item.ActionByEmployeeName,
                    ActionByEmployeeDesginationItemId = item.ActionByEmployeeDesginationItemId,
                    ActionByEmployeeDesginationTitle = item.ActionByEmployeeDesginationTitle,
                    ActionByEmployeeOfficeItemId = item.ActionByEmployeeOfficeItemId,
                    ActionByEmployeeOfficeName = item.ActionByEmployeeOfficeName,
                    ActionByEmployeeOfficeCode = item.ActionByEmployeeOfficeCode,
                    Status = EnumProductApplicationStatus.CorrectionPending
                };

                history.AddEntityBasicInfo();

                newHistory.Add(history);
            }

            var memberInformation = _repository.GetItems<BuroMember>(m => m.ItemId == loanApplicationInformation.MemberItemId)
                .Select(m => new
                {
                    m.MemberName,
                    m.CenterName,
                })
                .FirstOrDefault();

            var notifiableEmployee = _repository.GetItems<BuroEmployee>(e => e.ItemId == loanApplicationInformation.Initiator.ItemId)
                .Select(e => new
                {
                    e.DesignationCode,
                    e.UserItemId,
                })
                .FirstOrDefault();

            if (memberInformation == null || notifiableEmployee == null)
            {
                return;
            }

            var responseKey = BuroLoanApplicationNotificationKeys.GetNotificationKeyByDesignationCode(notifiableEmployee.DesignationCode, EnumLoanActions.Invited);

            var payload = new
            {
                AssignedAt = DateTime.UtcNow,
                AssignedBy = currentEmployee.UserItemId,
                command.LoanApplicationItemId,
                PersonName = memberInformation.MemberName,
                PersonCenterName = memberInformation.CenterName,
                RequestedByEmployeeName = currentEmployee.EmployeeName,
                RequestedByEmployeePin = currentEmployee.EmployeePin,
                RequestedByEmployeeDesignation = currentEmployee.DesignationTitle,
                RequestedByEmployeeOfficeName = currentEmployee.CurrentOfficeTitle,
                RequestedByEmployeeOfficeCode = currentEmployee.CurrentOfficeCode,
                command.Remarks,
            };

            await _sharedService.NotifyUserAsync(responseKey, payload, command.LoanApplicationItemId, notifiableEmployee.UserItemId);
        }

        private async Task HandleLoanApplicationRejectionAsync(LoanApplicationPostProcessingEvent command, BuroEmployee currentEmployee)
        {
            var loanAction = command.Status switch
            {
                EnumProductApplicationStatus.Rejected => EnumLoanActions.Rejected,
                EnumProductApplicationStatus.Discarded => EnumLoanActions.Discarded,
                _ => default
            };

            var actionByEmployeeItemIds = _repository.GetItems<BuroProductApplicationHistory>(
                a => a.ApplicationItemId == command.LoanApplicationItemId
                  && a.ActionByEmployeeItemId != currentEmployee.ItemId
                  && a.ProductType == EnumProductType.Loan
                  && (a.Status != EnumProductApplicationStatus.Draft
                  || a.Status != EnumProductApplicationStatus.Rejected
                  || a.Status != EnumProductApplicationStatus.Discarded))
                .Select(h => h.ActionByEmployeeItemId)
                .Distinct();

            var loanAppliedByMemberItemId = _repository.GetItems<BuroLoanApplication>(l => l.ItemId == command.LoanApplicationItemId)
                .Select(l => l.MemberItemId)
                .FirstOrDefault();

            var memberInformation = _repository.GetItems<BuroMember>(m => m.ItemId == loanAppliedByMemberItemId)
                .Select(m => new
                {
                    m.MemberName,
                    m.CenterName,
                })
                .FirstOrDefault();

            var notifiableEmployee = _repository.GetItems<BuroEmployee>(e => actionByEmployeeItemIds.Contains(e.ItemId))
                .Select(e => new
                {
                    e.DesignationCode,
                    e.UserItemId,
                });

            var payload = new
            {
                AssignedAt = DateTime.UtcNow,
                AssignedBy = currentEmployee.UserItemId,
                command.LoanApplicationItemId,
                PersonName = memberInformation?.MemberName ?? string.Empty,
                PersonCenterName = memberInformation?.CenterName ?? string.Empty,
                RequestedByEmployeeName = currentEmployee.EmployeeName,
                RequestedByEmployeePin = currentEmployee.EmployeePin,
                RequestedByEmployeeDesignation = currentEmployee.DesignationTitle,
                RequestedByEmployeeOfficeName = currentEmployee.CurrentOfficeTitle,
                RequestedByEmployeeOfficeCode = currentEmployee.CurrentOfficeCode,
                command.Remarks,
            };

            foreach (var employee in notifiableEmployee)
            {
                var responseKey = BuroLoanApplicationNotificationKeys.GetNotificationKeyByDesignationCode(employee.DesignationCode, loanAction);

                await _sharedService.NotifyUserAsync(responseKey, payload, command.LoanApplicationItemId, employee.UserItemId);
            }
        }
    }
}
