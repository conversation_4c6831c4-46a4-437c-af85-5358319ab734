using EventHandlers.Contracts;
using Infrastructure.Constants;
using Infrastructure.Events.Account;
using Infrastructure.Extensions;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Product;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace EventHandlers.Services.Account
{
    public class GSChangeApprovalEventService : IGSChangeApprovalEventService
    {
        private readonly ILogger<GSChangeApprovalEventService> _logger;
        private readonly ISecurityContextProvider _securityContextProvider;
        private readonly IRepository _repository;
        private readonly ISharedService _sharedService;

        public GSChangeApprovalEventService(
            ILogger<GSChangeApprovalEventService> logger,
            ISecurityContextProvider securityContextProvider,
            IRepository repository,
            ISharedService sharedService)
        {
            _logger = logger;
            _securityContextProvider = securityContextProvider;
            _repository = repository;
            _sharedService = sharedService;
        }

        public async Task ChangeGSAccount(GSChangeApprovalEvent command)
        {
            _logger.LogInformation("Processing GSChangeApprovalEvent for Approval ID: {ApprovalItemId}", command.ApprovalItemId);

            var approval = await _repository.GetItemAsync<BuroApproval>(x => x.ItemId == command.ApprovalItemId);
            if (approval == null)
            {
                _logger.LogWarning("No approval found for Approval ID: {ApprovalItemId}", command.ApprovalItemId);
                return;
            }

            var memberItemId = approval.MetaData?.FirstOrDefault(m => m.Key == "MemberItemId")?.Value;
            var productLineItemId = approval.MetaData?.FirstOrDefault(m => m.Key == "NewProductLineItemId")?.Value;

            _logger.LogInformation("Extracted metadata -> MemberItemId: {MemberItemId}, NewProductLineItemId: {NewProductLineItemId}", memberItemId, productLineItemId);

            var member = await _repository.GetItemAsync<BuroMember>(x => x.ItemId == memberItemId);
            if (member == null)
            {
                _logger.LogWarning("No member found for MemberItemId: {MemberItemId}", memberItemId);
                return;
            }

            var securityContext = _securityContextProvider.GetSecurityContext();

            if (command.IsAccepted)
            {
                await HandleAcceptedApproval(member, productLineItemId, approval, securityContext);
            }

            await SendApprovalNotificationAsync(command, approval, member);

            _logger.LogInformation("Finished processing GSChangeApprovalEvent for Approval ID: {ApprovalItemId}", command.ApprovalItemId);
        }

        private async Task HandleAcceptedApproval(BuroMember member, string productLineItemId, BuroApproval approval, SecurityContext securityContext)
        {
            _logger.LogInformation("Handling accepted GS account change for MemberItemId: {MemberItemId}", member.ItemId);

            var gsProductLine = await _repository.GetItemAsync<GeneralSavingProductLine>(x => x.ItemId == productLineItemId);
            var center = await _repository.GetItemAsync<BuroCenter>(c => c.ItemId == member.CenterItemId);
            var oldGSAccount = await _repository.GetItemAsync<BuroGeneralSavingsAccount>(x => x.ItemId == approval.RelatedEntityItemId);

            var newAccount = await CreateNewGSAccount(gsProductLine, member, center, oldGSAccount);
            newAccount.AddEntityBasicInfo();

            await _repository.SaveAsync(newAccount);

            _logger.LogInformation("Created new GS account with ID: {NewGSAccountId}", newAccount.ItemId);

            await ArchiveOldGSAccount(oldGSAccount, securityContext);
        }

        private async Task<BuroGeneralSavingsAccount> CreateNewGSAccount(
            GeneralSavingProductLine gsProductLine,
            BuroMember member,
            BuroCenter center,
            BuroGeneralSavingsAccount oldGSAccount)
        {
            var accountCode = string.Concat(nameof(EnumProductType.GeneralSaving).Where(char.IsUpper));
            var accountSequenceNumber = await _sharedService.GetSequenceNumberAsync(BuroConstants.BuroGeneralSavingsAccountIdentifier, accountCode);

            var adjustedInterestRate = CalculateAdjustedInterestRate(gsProductLine, member);

            _logger.LogInformation("Generated AccountCode: {AccountCode}, SequenceNumber: {SequenceNumber}", accountCode, accountSequenceNumber);

            return new BuroGeneralSavingsAccount
            {
                AccountCode = accountCode,
                AccountSequenceNumber = accountSequenceNumber,
                AccountStatus = EnumProductAccountStatus.Active,
                OpeningBranchItemId = member.BranchOfficeItemId,
                OpeningBranchName = member.BranchOfficeName,
                CurrentBranchItemId = member.BranchOfficeItemId,
                CurrentBranchName = member.BranchOfficeName,
                OpeningDate = DateTime.UtcNow,
                InterestRate = adjustedInterestRate,
                MemberItemId = member.ItemId,
                PersonItemId = member.PersonItemId,
                ProductLineItemId = gsProductLine.ItemId,
                ProductLineName = gsProductLine.LineName,
                ProgramOrganizerEmployeeItemId = center?.ProgramOrganizerEmployeeItemId ?? string.Empty,
                ProgramOrganizerEmployeeName = center?.ProgramOrganizerEmployeeName ?? string.Empty,
                MemberName = member.MemberName,
                MemberSequenceNumber = member.MemberSequenceNumber,
                MemberBranchOfficeItemId = member.BranchOfficeItemId,
                MemberBranchOfficeName = member.BranchOfficeName,
                Balance = oldGSAccount.Balance + oldGSAccount.AccuredInterest
            };
        }

        private static double CalculateAdjustedInterestRate(GeneralSavingProductLine productLine, BuroMember member)
        {
            var baseInterestRate = productLine.InterestRate;

            if (productLine.AreaSpecificInterests == null || !productLine.AreaSpecificInterests.Any())
                return baseInterestRate;

            foreach (var areaInterest in productLine.AreaSpecificInterests)
            {
                if (areaInterest.CenterSamityItemIds.Contains(member.CenterItemId) ||
                    areaInterest.BranchItemIds.Contains(member.BranchOfficeItemId))
                {
                    return areaInterest.AdjustmentType switch
                    {
                        InterestAdjustmentType.Increment => baseInterestRate + areaInterest.AdjustmentValue,
                        InterestAdjustmentType.Decrement => baseInterestRate - areaInterest.AdjustmentValue,
                        _ => baseInterestRate
                    };
                }
            }

            return baseInterestRate;
        }

        private async Task ArchiveOldGSAccount(BuroGeneralSavingsAccount oldAccount, SecurityContext securityContext)
        {
            _logger.LogInformation("Archiving old GS account with ID: {OldAccountId}", oldAccount.ItemId);

            var updates = new Dictionary<string, object>
            {
                [nameof(BuroGeneralSavingsAccount.LastUpdateDate)] = DateTime.UtcNow,
                [nameof(BuroGeneralSavingsAccount.LastUpdatedBy)] = securityContext.UserId,
                [nameof(BuroGeneralSavingsAccount.AccountStatus)] = EnumProductAccountStatus.Archived,
                [nameof(BuroGeneralSavingsAccount.Balance)] = 0,
                [nameof(BuroGeneralSavingsAccount.AccuredInterest)] = 0
            };

            await _repository.UpdateAsync<BuroGeneralSavingsAccount>(x => x.ItemId == oldAccount.ItemId, updates);

            _logger.LogInformation("Archived old GS account successfully.");
        }

        private async Task SendApprovalNotificationAsync(GSChangeApprovalEvent command, BuroApproval approval, BuroMember member)
        {
            var responseKey = command.IsAccepted
                ? BuroNotificationKeys.GSAccountChangeRequestApproved
                : BuroNotificationKeys.GSAccountChangeRequestRejected;

            var payload = new
            {
                AssignedAt = DateTime.UtcNow,
                ApprovalItemId = approval.ItemId,
                ApproverName = approval.ActionedByEmployeeName,
                ApproverPin = approval.ActionedByEmployeePIN,
                ApproverDesignation = approval.ActionedByEmployeeDesignation,
                MemberItemId = member.ItemId,
                member.MemberName,
                member.CenterName,
            };

            _logger.LogInformation("Sending GS change approval notification: {NotificationKey} for Member: {MemberName}", responseKey, member.MemberName);

            await _sharedService.NotifyUserAsync(
                responseKey,
                payload,
                command.ApprovalItemId,
                approval.CreatedBy);

            _logger.LogInformation("Notification sent to user ID: {UserId}", approval.CreatedBy);
        }
    }
}