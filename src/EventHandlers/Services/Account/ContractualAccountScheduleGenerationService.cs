using EventHandlers.Contracts;
using FinMongoDbRepositories;
using Infrastructure.Constants;
using Infrastructure.Events.Account;
using Infrastructure.Extensions;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace EventHandlers.Services.Account
{
    public class ContractualAccountScheduleGenerationService : IContractualAccountScheduleGenerationService
    {
        private readonly ILogger<ContractualAccountScheduleGenerationService> _logger;
        private readonly IRepository _repository;
        private readonly IFinMongoDbRepository _finMongoDbRepository;
        private readonly IWorkingDayService _workingDayService;
        private readonly ISecurityContextProvider _securityContextProvider;

        public ContractualAccountScheduleGenerationService(
            ILogger<ContractualAccountScheduleGenerationService> logger,
            IRepository repository,
            IFinMongoDbRepository finMongoDbRepository,
            IWorkingDayService workingDayService,
            ISecurityContextProvider securityContextProvider)
        {
            _logger = logger;
            _repository = repository;
            _finMongoDbRepository = finMongoDbRepository;
            _workingDayService = workingDayService;
            _securityContextProvider = securityContextProvider;
        }

        public async Task GenerateContractualAccountSchedule(ContractualAccountScheduleGenerationEvent command)
        {
            _logger.LogInformation("Starting schedule generation for AccountItemId: {AccountItemId}", command.ContractualAccountItemId);

            var (account, member, center) = await GetRequiredEntities(command.ContractualAccountItemId);
            if (account == null || member == null || center == null) return;

            var holidayHashes = await _workingDayService.GetHolidaySetAsync(DateTime.UtcNow, center.ItemId, member.BranchOfficeItemId, member.ItemId);

            var weekend = (await _finMongoDbRepository.FindAsync<BuroWeekend>(_ => true)).FirstOrDefault();

            var schedules = await GenerateSchedules(account, center, holidayHashes, weekend);
            if (!schedules.Any())
            {
                _logger.LogWarning("No schedules generated for AccountItemId: {AccountItemId}", account.ItemId);
                return;
            }

            await _repository.SaveAsync(schedules);

            var maturityDate = CalculateMaturityDate(account, schedules);
            await UpdateMaturityDateAsync(account.ItemId, maturityDate);

            _logger.LogInformation("Generated {Count} schedules for AccountItemId: {AccountItemId}", schedules.Count, account.ItemId);
        }

        private async Task<(BuroContractualSavingsAccount, BuroMember, BuroCenter)> GetRequiredEntities(string accountItemId)
        {
            var account = await _repository.GetItemAsync<BuroContractualSavingsAccount>(x => x.ItemId == accountItemId);
            if (account == null)
            {
                _logger.LogWarning("Contractual account not found for ItemId: {AccountItemId}", accountItemId);
                return (null, null, null);
            }

            var member = await _repository.GetItemAsync<BuroMember>(x => x.ItemId == account.MemberItemId);
            if (member == null)
            {
                _logger.LogWarning("Member not found for ItemId: {MemberItemId}", account.MemberItemId);
                return (null, null, null);
            }

            var center = await _repository.GetItemAsync<BuroCenter>(x => x.ItemId == member.CenterItemId);
            if (center == null)
            {
                _logger.LogWarning("Center not found for ItemId: {CenterItemId}", member.CenterItemId);
                return (null, null, null);
            }

            return (account, member, center);
        }

        private async Task<List<BuroMemberSchedule>> GenerateSchedules(
            BuroContractualSavingsAccount account,
            BuroCenter center,
            HashSet<DateTime> holidayHashes,
            BuroWeekend weekend)
        {
            var totalInstallments = account.OriginalTenureDetails.NumberOfInstallment;
            var installment = 1;

            var schedules = new List<BuroMemberSchedule>
            {
                MakeNewMemberSchedule(account, center.Type, installment, totalInstallments, DateTime.UtcNow, PaymentStatus.Paid, account.InstalmentAmount)
            };

            var nextScheduleDate = GetInitialScheduleDate(DateTime.UtcNow, account.PaymentInterval, center.CenterDay);

            for (installment = installment + 1; installment <= totalInstallments; installment++)
            {
                var schedule = MakeNewMemberSchedule(account, center.Type, installment, totalInstallments, nextScheduleDate, PaymentStatus.Pending);

                schedules.Add(schedule);

                nextScheduleDate = account.PaymentInterval switch
                {
                    EnumPaymentInterval.Weekly => await GetNextWeeklyScheduleDate(nextScheduleDate, holidayHashes, weekend),
                    EnumPaymentInterval.Monthly => GetNextMonthlyScheduleDate(nextScheduleDate),
                    _ => throw new NotSupportedException($"Unsupported PaymentInterval: {account.PaymentInterval}")
                };
            }

            return schedules;
        }

        private static BuroMemberSchedule MakeNewMemberSchedule(
            BuroContractualSavingsAccount account,
            EnumCollectionGroupType collectionGroupType,
            int installmentNUmber,
            int totalInstallments,
            DateTime nextScheduleDate,
            PaymentStatus paymentStatus,
            double? paidAmount = null)
        {
            var schedule = new BuroMemberSchedule
            {
                AccountItemId = account.ItemId,
                AccountCode = account.AccountCode,
                ProductType = EnumProductType.ContractualSaving,
                AccountSequenceNumber = account.AccountSequenceNumber,
                MemberItemId = account.MemberItemId,
                OriginalInstallmentNumber = installmentNUmber,
                ActualInstallmentNumber = installmentNUmber,
                TotalInstallmentCount = totalInstallments,
                CollectionGroupType = collectionGroupType,
                PaymentInterval = account.PaymentInterval,
                OriginalPaymentDate = nextScheduleDate,
                ActualPaymentDate = nextScheduleDate,
                PayableAmount = (double)account.InstalmentAmount,
                PaymentStatus = paymentStatus,
                IsAutoDebited = false,
                PaidAmount = paidAmount
            };

            schedule.AddEntityBasicInfo();

            return schedule;
        }

        private static DateTime GetInitialScheduleDate(DateTime currentDate, EnumPaymentInterval interval, DayOfWeek centerDay)
        {
            return interval switch
            {
                EnumPaymentInterval.Weekly => GetNextCenterDay(currentDate, centerDay),
                EnumPaymentInterval.Monthly => GetLastDayOfNextMonth(currentDate),
                _ => currentDate
            };
        }

        private static DateTime GetNextCenterDay(DateTime currentDate, DayOfWeek centerDay)
        {
            int daysUntilNext = ((int)centerDay - (int)currentDate.DayOfWeek + BuroProductConstants.DaysInWeek) % BuroProductConstants.DaysInWeek;
            return currentDate.AddDays(daysUntilNext == 0 ? BuroProductConstants.DaysInWeek : daysUntilNext);
        }

        private static DateTime GetLastDayOfNextMonth(DateTime date)
        {
            var nextMonth = date.AddMonths(1);
            return new DateTime(nextMonth.Year, nextMonth.Month, DateTime.DaysInMonth(nextMonth.Year, nextMonth.Month));
        }

        private static DateTime GetNextMonthlyScheduleDate(DateTime current)
        {
            return GetLastDayOfNextMonth(current);
        }

        private async Task<DateTime> GetNextWeeklyScheduleDate(DateTime current, HashSet<DateTime> holidayHashes, BuroWeekend weekend)
        {
            var nextDate = current.AddDays(BuroProductConstants.DaysInWeek);
            while (await _workingDayService.IsWeekendAsync(nextDate, weekend) ||
                await _workingDayService.IsHolidayAsync(nextDate, holidayHashes))
            {
                _logger.LogWarning("Weekly date {Date} falls on holiday, skipping...", nextDate);
                nextDate = nextDate.AddDays(BuroProductConstants.DaysInWeek);
            }
            return nextDate;
        }

        private static DateTime CalculateMaturityDate(BuroContractualSavingsAccount account, List<BuroMemberSchedule> schedules)
        {
            var baseDate = schedules[^1].OriginalPaymentDate;
            var provision = account.OriginalTenureDetails?.ProvisionPeriod;
            if (provision == null) return baseDate;

            return provision.Unit switch
            {
                EnumTenureUnit.Week => baseDate.AddDays(provision.Value * 7),
                EnumTenureUnit.Month => baseDate.AddMonths(provision.Value),
                EnumTenureUnit.Year => baseDate.AddYears(provision.Value),
                _ => baseDate
            };
        }

        private async Task UpdateMaturityDateAsync(string accountItemId, DateTime maturityDate)
        {
            var context = _securityContextProvider.GetSecurityContext();

            var updates = new Dictionary<string, object>
            {
                [nameof(BuroContractualSavingsAccount.LastUpdatedBy)] = context.UserId,
                [nameof(BuroContractualSavingsAccount.LastUpdateDate)] = DateTime.UtcNow,
                [nameof(BuroContractualSavingsAccount.OriginalMaturityDate)] = maturityDate,
                [nameof(BuroContractualSavingsAccount.ActualMaturityDate)] = maturityDate
            };

            await _repository.UpdateAsync<BuroContractualSavingsAccount>(
                x => x.ItemId == accountItemId, updates);

            _logger.LogInformation("Maturity date updated to {MaturityDate} for AccountItemId: {AccountItemId}", maturityDate, accountItemId);
        }
    }
}