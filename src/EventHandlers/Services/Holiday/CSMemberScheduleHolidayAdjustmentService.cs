using EventHandlers.Contracts;
using FinMongoDbRepositories;
using Infrastructure.Constants;
using Infrastructure.Events.Holiday;
using Infrastructure.Extensions;
using Microsoft.Extensions.Logging;
using MongoDB.Driver;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace EventHandlers.Services.Holiday
{
    public class CSMemberScheduleHolidayAdjustmentService : ICSMemberScheduleHolidayAdjustmentService
    {
        private readonly ILogger<CSMemberScheduleHolidayAdjustmentService> _logger;
        private readonly ISecurityContextProvider _securityContextProvider;
        private readonly IWorkingDayService _workingDayService;
        private readonly IFinMongoDbRepository _finMongoDbRepository;

        public CSMemberScheduleHolidayAdjustmentService(
            ILogger<CSMemberScheduleHolidayAdjustmentService> logger,
            ISecurityContextProvider securityContextProvider,
            IWorkingDayService workingDayService,
            IFinMongoDbRepository finMongoDbRepository)
        {
            _logger = logger;
            _securityContextProvider = securityContextProvider;
            _workingDayService = workingDayService;
            _finMongoDbRepository = finMongoDbRepository;
        }
        public async Task AdjustCSMemberSchedule(HolidayCreationEvent command, BuroMember member)
        {
            var startDate = command.HolidayStartDate!.Value.Date;
            var endDate = command.HolidayEndDate.Value!.Date;
            var today = DateTime.UtcNow;
            var securityContext = _securityContextProvider.GetSecurityContext();

            var csAccounts = await _finMongoDbRepository.FindAsync<BuroContractualSavingsAccount>(
                x => x.MemberItemId == member.ItemId && x.PaymentInterval == EnumPaymentInterval.Weekly);

            var holidayHashes = await _workingDayService.GetHolidaySetAsync(DateTime.UtcNow, member.CenterItemId, member.BranchOfficeItemId, member.ItemId);

            var weekend = (await _finMongoDbRepository.FindAsync<BuroWeekend>(_ => true)).FirstOrDefault();

            foreach (var account in csAccounts)
            {
                var allSchedules = await GetAllSchedulesAsync(account.ItemId, startDate);
                var holidayAffectedSchedules = allSchedules.Where(s => s.ActualPaymentDate.Date <= endDate).ToList();
                var upcomingSchedules = allSchedules.Where(s => s.ActualPaymentDate.Date > endDate).ToList();

                var countShift = holidayAffectedSchedules.Count;
                if (countShift == 0)
                    continue;

                await ShiftUpcomingInstallmentsAsync(upcomingSchedules, countShift);
                await AppendNewSchedulesAsync(allSchedules, countShift, holidayHashes, weekend);
                await UpdateMaturity(account, countShift);
            }
        }

        private async Task UpdateMaturity(BuroContractualSavingsAccount account, int countShift)
        {
            if (!account.ActualMaturityDate.HasValue)
            {
                _logger.LogWarning("Account {AccountItemId} has no maturity date. Skipping maturity update.", account.ItemId);
                return;
            }

            var newMaturity = account.ActualMaturityDate.Value.AddDays(countShift * BuroProductConstants.DaysInWeek);

            var securityContext = _securityContextProvider.GetSecurityContext();

            var updates = new Dictionary<string, object>
            {
                { nameof(BuroContractualSavingsAccount.LastUpdateDate), DateTime.UtcNow },
                { nameof(BuroContractualSavingsAccount.LastUpdatedBy), securityContext.UserId },
                { nameof(BuroContractualSavingsAccount.ActualMaturityDate), newMaturity }
            };

            await _finMongoDbRepository.UpdateOneAsync<BuroContractualSavingsAccount>(x => x.ItemId == account.ItemId, updates);
        }

        private async Task<List<BuroMemberSchedule>> GetAllSchedulesAsync(string accountItemId, DateTime startDate)
        {
            var filter = Builders<BuroMemberSchedule>.Filter.And(
                Builders<BuroMemberSchedule>.Filter.Eq(s => s.ProductType, EnumProductType.ContractualSaving),
                Builders<BuroMemberSchedule>.Filter.Eq(s => s.PaymentInterval, EnumPaymentInterval.Weekly),
                Builders<BuroMemberSchedule>.Filter.Eq(s => s.AccountItemId, accountItemId),
                Builders<BuroMemberSchedule>.Filter.Gte(s => s.ActualPaymentDate, startDate)
            );

            var allSchedules = (await _finMongoDbRepository.FindAsync(filter))
                .OrderBy(s => s.ActualInstallmentNumber)
                .ToList();

            return allSchedules;
        }

        private async Task ShiftUpcomingInstallmentsAsync(List<BuroMemberSchedule> upcomingSchedules, int countShift)
        {
            if (!upcomingSchedules.Any()) return;

            var securityContext = _securityContextProvider.GetSecurityContext();
            var today = DateTime.UtcNow;
            var ids = upcomingSchedules.Select(s => s.ItemId).ToList();

            var update = Builders<BuroMemberSchedule>.Update
                .Inc(s => s.ActualInstallmentNumber, -countShift)
                .Set(s => s.LastUpdateDate, today)
                .Set(s => s.LastUpdatedBy, securityContext.UserId);

            await _finMongoDbRepository.UpdateManyAsync(x => ids.Contains(x.ItemId), update);
        }

        private async Task AppendNewSchedulesAsync(List<BuroMemberSchedule> allSchedules, int countShift, HashSet<DateTime> holidayHashes, BuroWeekend weekend)
        {
            if (!allSchedules.Any()) return;

            var lastSchedule = allSchedules.Last();

            var lastDate = lastSchedule.ActualPaymentDate;
            var newSchedules = new List<BuroMemberSchedule>();

            for (int i = 1; i <= countShift; i++)
            {
                var nextDate = await GetNextWeeklyCSScheduleDate(lastDate, holidayHashes, weekend);

                var newSchedule = new BuroMemberSchedule
                {
                    AccountItemId = lastSchedule.AccountItemId,
                    AccountCode = lastSchedule.AccountCode,
                    AccountSequenceNumber = lastSchedule.AccountSequenceNumber,
                    MemberItemId = lastSchedule.MemberItemId,
                    ProductType = lastSchedule.ProductType,
                    PaymentInterval = lastSchedule.PaymentInterval,
                    CollectionGroupType = lastSchedule.CollectionGroupType,
                    OriginalInstallmentNumber = lastSchedule.OriginalInstallmentNumber + i,
                    ActualInstallmentNumber = lastSchedule.ActualInstallmentNumber + i,
                    OriginalPaymentDate = nextDate,
                    ActualPaymentDate = nextDate,
                    PayableAmount = lastSchedule.PayableAmount,
                    PaymentStatus = PaymentStatus.Pending,
                    IsAutoDebited = lastSchedule.IsAutoDebited
                };

                lastDate = nextDate;

                newSchedule.AddEntityBasicInfo();
                newSchedules.Add(newSchedule);
            }

            if (newSchedules.Any())
                await _finMongoDbRepository.InsertManyAsync(newSchedules);
        }

        private async Task<DateTime> GetNextWeeklyCSScheduleDate(DateTime current, HashSet<DateTime> holidayHashSet, BuroWeekend weekend)
        {
            var nextDate = current.AddDays(BuroProductConstants.DaysInWeek);
            while (await _workingDayService.IsWeekendAsync(nextDate, weekend) ||
                await _workingDayService.IsHolidayAsync(nextDate, holidayHashSet))
            {
                _logger.LogWarning("Weekly date {Date} falls on holiday, skipping...", nextDate);
                nextDate = nextDate.AddDays(BuroProductConstants.DaysInWeek);
            }
            return nextDate;
        }
    }
}
