using EventHandlers.Contracts;
using FinMongoDbRepositories;
using Microsoft.Extensions.Logging;
using MongoDB.Driver;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;

namespace EventHandlers.Services.Holiday
{
    public class WorkingDayService : IWorkingDayService
    {
        private readonly ILogger<WorkingDayService> _logger;
        private readonly IFinMongoDbRepository _finMongoDbRepository;

        public WorkingDayService(ILogger<WorkingDayService> logger,
            IFinMongoDbRepository finMongoDbRepository)
        {
            _logger = logger;
            _finMongoDbRepository = finMongoDbRepository;
        }

        public async Task<HashSet<DateTime>> GetHolidaySetAsync(DateTime startDateTime, string centerItemId, string officeItemId, string memberItemId)
        {
            _logger.LogInformation("Fetching holidays from {StartDate} for Center: {CenterId}", startDateTime, centerItemId);

            var startDate = startDateTime.Date;
            var filterBuilder = Builders<BuroHoliday>.Filter;

            var orFilters = BuildHolidayFilters(filterBuilder, centerItemId, officeItemId, memberItemId);

            var finalFilter = filterBuilder.And(
                filterBuilder.Eq(x => x.IsActive, true),
                filterBuilder.Gte(x => x.HolidayEndDate, startDate),
                filterBuilder.Or(orFilters)
            );

            var holidays = await _finMongoDbRepository.FindWithOptionAsync(finalFilter);

            if (holidays == null || !holidays.Any())
            {
                _logger.LogWarning("No holidays found for Center: {CenterId} from {StartDate}", centerItemId, startDate);
                return new HashSet<DateTime>();
            }

            var holidaySet = new HashSet<DateTime>();

            foreach (var holiday in holidays)
            {
                if (holiday.HolidayStartDate is { } start && holiday.HolidayEndDate is { } end)
                {
                    for (var date = start; date <= end; date = date.AddDays(1))
                    {
                        holidaySet.Add(date.Date);
                    }
                }
            }

            _logger.LogInformation("Loaded {Count} holiday dates for Center: {CenterId}", holidaySet.Count, centerItemId);
            return holidaySet;
        }

        private static List<FilterDefinition<BuroHoliday>> BuildHolidayFilters(FilterDefinitionBuilder<BuroHoliday> filterBuilder,
            string centerItemId, string officeItemId, string memberItemId)
        {
            var filters = new List<FilterDefinition<BuroHoliday>>
            {
                filterBuilder.Eq(x => x.HolidayType, EnumHolidayType.Global)
            };

            if (!string.IsNullOrEmpty(centerItemId))
            {
                filters.Add(filterBuilder.And(
                    filterBuilder.Eq(x => x.HolidayType, EnumHolidayType.CenterSpecific),
                    filterBuilder.Eq(x => x.CenterItemId, centerItemId)
                ));
            }

            if (!string.IsNullOrEmpty(officeItemId))
            {
                filters.Add(filterBuilder.And(
                    filterBuilder.Eq(x => x.HolidayType, EnumHolidayType.OfficeSpecific),
                    filterBuilder.Eq(x => x.OfficeItemId, officeItemId)
                ));
            }

            if (!string.IsNullOrEmpty(memberItemId))
            {
                filters.Add(filterBuilder.And(
                    filterBuilder.Eq(x => x.HolidayType, EnumHolidayType.Personal),
                    filterBuilder.Eq(x => x.MemberItemId, memberItemId)
                ));
            }

            return filters;
        }

        public async Task<bool> IsWeekendAsync(DateTime date, BuroWeekend weekend = null)
        {
            var day = date.DayOfWeek;
            List<DayOfWeek> weekendDays;

            weekend ??= (await _finMongoDbRepository.FindAsync<BuroWeekend>(_ => true)).FirstOrDefault();

            weekendDays = weekend?.DaysOfWeek?
                .Select(i => i)
                .ToList() ?? new List<DayOfWeek>();

            return weekendDays.Contains(day);
        }

        public async Task<bool> IsHolidayAsync(DateTime date, HashSet<DateTime> holidayHashSet, string centerItemId = null, string officeItemId = null, string memberItemId = null)
        {

            holidayHashSet ??= await GetHolidaySetAsync(date, centerItemId, officeItemId, memberItemId);

            return holidayHashSet.Contains(date.Date);
        }
    }

}
