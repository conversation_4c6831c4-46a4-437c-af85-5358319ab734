using EventHandlers.Contracts;
using Infrastructure.Events.ConsentForm;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace EventHandlers.Services.ConsentForm;

public class ConsentFormApprovalEventService : IConsentFormApprovalEventService
{
    private readonly ILogger<ConsentFormApprovalEventService> _logger;
    private readonly ISecurityContextProvider _securityContextProvider;
    private readonly IRepository _repository;

    public ConsentFormApprovalEventService(
        ILogger<ConsentFormApprovalEventService> logger,
        ISecurityContextProvider securityContextProvider,
        IRepository repository)
    {
        _logger = logger;
        _securityContextProvider = securityContextProvider;
        _repository = repository;
    }

    public async Task HandleConsentFormApprovalEventAsync(ConsentFormApprovalEvent command)
    {
        _logger.LogInformation("Starting ConsentFormApprovalRequestAsync for Request Id: {ConsentFormItemId}", command.ConsentFormItemId);

        var currentUserId = GetCurrentUserId();
        await ConsentFormApprovalRequestAsync(command, currentUserId);
        _logger.LogInformation("Successfully updated consent form status: {ConsentFormItemId}", command.ConsentFormItemId);
    }


    private async Task ConsentFormApprovalRequestAsync(ConsentFormApprovalEvent command, string currentUserId)
    {
        _logger.LogInformation("ConsentFormApprovalEventService -> HandleConsentFormApprovalEventAsync-> ConsentFormApprovalRequestAsync-> START");
        var updatedProperties = PrepareToBeUpdatedData(command, currentUserId);
        await _repository.UpdateManyAsync<BuroConsentForm>(x => x.ItemId == command.ConsentFormItemId, updatedProperties);
        _logger.LogInformation("ConsentFormApprovalEventService -> HandleConsentFormApprovalEventAsync-> ConsentFormApprovalRequestAsync->  END");
    }


    private Dictionary<string, object> PrepareToBeUpdatedData(ConsentFormApprovalEvent command, string currentUserId)
    {
        return new Dictionary<string, object>
        {
            { nameof(BuroConsentForm.LastUpdateDate), DateTime.UtcNow },
            { nameof(BuroConsentForm.LastUpdatedBy), currentUserId },
            { nameof(BuroConsentForm.Status), GetConsentFormApprovalStatus(command.IsAccepted) }
        };
    }

    private static EnumConsentFormApprovalStatus GetConsentFormApprovalStatus(bool isAccepted)
    {
        return isAccepted ? EnumConsentFormApprovalStatus.Approved : EnumConsentFormApprovalStatus.Rejected;
    }

    private string GetCurrentUserId()
    {
        var securityContext = _securityContextProvider.GetSecurityContext();
        return securityContext.UserId;
    }




}