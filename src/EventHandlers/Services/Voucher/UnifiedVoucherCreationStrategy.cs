using EventHandlers.Contracts;
using FinMongoDbRepositories;
using Infrastructure.Constants;
using Infrastructure.Contracts;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace EventHandlers.Services.Voucher;

public class UnifiedVoucherCreationStrategy : IVoucherCreationStrategy
{

    private readonly ILogger<UnifiedVoucherCreationStrategy> _logger;
    private readonly IFinMongoDbRepository _finMongoDbRepository;
    private readonly ISharedService _sharedService;
    private readonly VoucherConfigurationProviderFactory _configProviderFactory;

    public UnifiedVoucherCreationStrategy(
        ILogger<UnifiedVoucherCreationStrategy> logger,
        IFinMongoDbRepository finMongoDbRepository,
        ISharedService sharedService,
        VoucherConfigurationProviderFactory configProviderFactory)
    {
        _logger = logger;
        _finMongoDbRepository = finMongoDbRepository;
        _sharedService = sharedService;
        _configProviderFactory = configProviderFactory;
    }

    public bool CanHandle(EnumVoucherScopeCategory voucherScopeCategory)
    {
        return true;
    }

    public async Task ProcessVoucherAsync(IVoucherEvent command, SecurityContext securityContext)
    {
        var configProvider = _configProviderFactory.GetProvider(command.VoucherScopeCategory);
        var voucherConfig = await configProvider.GetVoucherConfigurationAsync(command);

        if (voucherConfig == null)
        {
            var configType = command.VoucherScopeCategory == EnumVoucherScopeCategory.Product
                ? "ProductLine-Voucher"
                : "Standard-Voucher";
            throw new ArgumentException($"There is no {configType} configuration found for the specified parameters.");
        }

        await CreateVouchers(command, voucherConfig, securityContext);

    }

    private async Task CreateVouchers(IVoucherEvent command, BuroVoucherConfigurationBase config, SecurityContext securityContext)
    {
        _logger.LogInformation("Preparing to insert voucher for scope: {VoucherScope}, type: {VoucherType}...",
            command.VoucherScopeCategory, config.VoucherCreationType);

        if (config.VoucherCreationType == EnumVoucherCreationType.Manual)
        {
            _logger.LogError("Manual voucher creation is not supported.");
            throw new ArgumentException("Manual voucher creation is not supported.");
        }

        var createdByEmployeePin = await _finMongoDbRepository.FindWithProjectionAsync<BuroEmployee, string>(
            x => x.UserItemId == securityContext.UserId,
            x => x.EmployeePin);

        var voucherCode = command.VoucherCode;
        if (string.IsNullOrWhiteSpace(command.VoucherCode))
        {
            voucherCode = await _sharedService.GetSequenceNumberAsync(
                BuroConstants.BuroVoucherIdentifier, "WA");
        }

        var vouchers = config.LedgerEntries
            .Select(ledger => new BuroVoucher
            {
                ItemId = Guid.NewGuid().ToString(),
                VoucherCode = voucherCode,
                VoucherGroupItemId = command.VoucherGroupItemId,
                ChartOfAccountItemId = ledger.ChartOfAccountItemId,
                OfficeItemId = command.OfficeItemId,
                ChartOfAccountCode = ledger.ChartOfAccountCode,
                ChartOfAccountName = ledger.ChartOfAccountName,
                VoucherCreationType = config.VoucherCreationType,
                VoucherScopeCategory = command.VoucherScopeCategory,
                RelatedEntityName = command.RelatedEntityName,
                RelatedEntityItemId = command.RelatedEntityItemId,
                TransactionTypeName = config.TransactionTypeName,
                TransactionTypeItemId = config.TransactionTypeItemId,
                VoucherType = ledger.VoucherType,
                DebitAmount = ledger.VoucherType == EnumBuroVoucherType.Debit
                    ? command.Amount : 0,
                CreditAmount = ledger.VoucherType == EnumBuroVoucherType.Credit
                    ? command.Amount : 0,
                Status = EnumVoucherStatus.Approved,
                IssueDate = DateTime.UtcNow,
                Narration = command.Narration,
                VoucherCreatedEmployeeName = securityContext.DisplayName,
                VoucherCreatedEmployeePin = createdByEmployeePin[0],
                Tags = new[] { Tags.IsAVoucher },
                CreatedBy = securityContext.UserId,
                LastUpdatedBy = securityContext.UserId,
            })
            .ToList();

        await _finMongoDbRepository.InsertManyAsync(vouchers);
        _logger.LogInformation("Successfully inserted voucher for scope: {VoucherScope}, type: {VoucherType}", command.VoucherScopeCategory, config.VoucherCreationType);
    }
}