using EventHandlers.Contracts;
using EventHandlers.Contracts.inventory;
using FinMongoDbRepositories;
using Infrastructure.Constants;
using Infrastructure.Enums;
using Infrastructure.Events.Inventory;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Inventory;

namespace EventHandlers.Services.Inventory;

public class InventoryTransferSourceApprovalService : BaseInventoryApprovalService
{
    private readonly ILogger<InventoryTransferSourceApprovalService> _logger;
    private readonly IFinMongoDbRepository _repository;
    private readonly IInventoryStockManagementService _stockManagementService;
    private readonly ISharedService _sharedService;

    public InventoryTransferSourceApprovalService(ILogger<InventoryTransferSourceApprovalService> logger,
        IFinMongoDbRepository repository,
        IInventoryStockManagementService stockManagementService,
        ISharedService sharedService) : base(repository, stockManagementService)
    {
        _logger = logger;
        _repository = repository;
        _stockManagementService = stockManagementService;
        _sharedService = sharedService;
    }

    public async Task ProcessSourceTransferApproval(InventoryTransferSourceApprovalEvent command)
    {
        var inventoryMovementPlan = await FetchInventoryMovementPlanAsync(command.InventoryMovementPlanItemId);
        if (inventoryMovementPlan == null)
        {
            _logger.LogError("Inventory movement plan not found");
            return;
        }

        await UpdateInventoryMovementPlanAsync(command, inventoryMovementPlan.ItemId);

        var (totalQuantity, totalPrice) = CalculateTotals(inventoryMovementPlan);

        var inventoryMovementOut = CreateInventoryMovement(
            inventoryMovementPlan,
            totalQuantity,
            totalPrice,
            inventoryMovementPlan.SourceOfficeItemId,
            inventoryMovementPlan.SourceOfficeName,
            inventoryMovementPlan.RecipientOfficeItemId,
            inventoryMovementPlan.RecipientOfficeName,
            EnumInventoryMovementType.Out);

        var inventoryMovementIn = CreateInventoryMovement(
            inventoryMovementPlan,
            totalQuantity,
            totalPrice,
            inventoryMovementPlan.RecipientOfficeItemId,
            inventoryMovementPlan.RecipientOfficeName,
            inventoryMovementPlan.SourceOfficeItemId,
            inventoryMovementPlan.SourceOfficeName,
            EnumInventoryMovementType.Out);

        await ManageInventoryStockChanges(command, inventoryMovementOut, inventoryMovementIn, inventoryMovementPlan);
    }

    private async Task ManageInventoryStockChanges(
        InventoryTransferSourceApprovalEvent command,
        BuroInventoryMovement inventoryMovementOut,
        BuroInventoryMovement inventoryMovementIn,
        BuroInventoryMovementPlan inventoryMovementPlan)
    {
        if (command.IsAccepted)
        {
            await ForwardApprovalToRecipientManager(inventoryMovementPlan);
        }
        else
        {
            await HandleRejectedStockChanges(inventoryMovementOut, inventoryMovementIn, inventoryMovementPlan);
        }
    }

    private async Task HandleRejectedStockChanges(BuroInventoryMovement inventoryMovementOut, BuroInventoryMovement inventoryMovementIn, BuroInventoryMovementPlan inventoryMovementPlan)
    {
        await UpdateInventoryStockAsync(inventoryMovementOut.InventoryItemId,
            inventoryMovementOut.SourceOfficeItemId,
            EnumInventoryMovementType.In,
            inventoryMovementOut.Quantity,
            inventoryMovementOut.UnitPrice,
            inventoryMovementOut.CreatedBy,
            EnumStockUpdateType.AvailableOnly);

        await UpdateInventoryStockAsync(inventoryMovementIn.InventoryItemId,
            inventoryMovementIn.SourceOfficeItemId,
            EnumInventoryMovementType.Out,
            inventoryMovementIn.Quantity,
            inventoryMovementIn.UnitPrice,
            inventoryMovementIn.CreatedBy,
            EnumStockUpdateType.AvailableOnly);

        await UpdateInventoryStockBreakdownAsync(EnumInventoryMovementType.In, inventoryMovementPlan,
            EnumStockUpdateType.AvailableOnly);
        await _stockManagementService.UpdateInventoryStockBreakdownAsync(inventoryMovementPlan, inventoryMovementIn,
            EnumInventoryMovementType.Out, EnumStockUpdateType.AvailableOnly);
    }

    private async Task ForwardApprovalToRecipientManager(BuroInventoryMovementPlan inventoryMovementPlan)
    {
        var sourceEmployee =
            await _repository.FindOneAsync<BuroEmployee>(item => item.UserItemId == inventoryMovementPlan.CreatedBy);
        var approvalId = await AskApprovalForInventoryTransferAsync(
            inventoryMovementPlan,
            sourceEmployee,
            inventoryMovementPlan.RecipientOfficeItemId,
            Tags.IsATransferInventoryRecipientApproval,
            EnumApprovalCategoryType.InventoryTransferRecipientApproval);

        await UpdateInventoryMovementPlanForRecipientApproval(inventoryMovementPlan, approvalId);
    }

    private async Task UpdateInventoryMovementPlanForRecipientApproval(BuroInventoryMovementPlan inventoryMovementPlan, string approvalId)
    {
        var updatedProperties = new Dictionary<string, object>
        {
            { nameof(BuroInventoryMovementPlan.TransferToApprovalItemId), approvalId },
            { nameof(BuroInventoryMovementPlan.ApprovalStatus), EnumInventoryApprovalStatus.PendingRecipientApproval }
        };
        await _repository.UpdateOneAsync<BuroInventoryMovementPlan>(item => item.ItemId == inventoryMovementPlan.ItemId,
            updatedProperties);
    }

    private async Task<string> AskApprovalForInventoryTransferAsync(BuroInventoryMovementPlan inventoryMovementPlan,
        BuroEmployee sourceEmployee, string approverOfficeItemId, string tag, EnumApprovalCategoryType categoryType)
    {
        var approverEmployee = await GetEmployeeByDesignationForAnOfficeAsync(
            approverOfficeItemId,
            BuroDesignationConstant.BranchManagerDesignationCode);

        var approvalItemId =
            await CreateInventoryItemTransferApprovalAsync(inventoryMovementPlan,
                sourceEmployee,
                approverEmployee,
                tag,
                categoryType);

        var notificationPayload = MakeNotificationPayloadForApprover(inventoryMovementPlan,
            sourceEmployee.EmployeeName, approvalItemId, approverEmployee);

        await _sharedService.NotifyUserAsync(BuroNotificationKeys.InventoryTransferItemBranchManagerInvited,
            notificationPayload, inventoryMovementPlan.ItemId, approverEmployee.UserItemId);
        return approvalItemId;
    }

    private async Task<string> CreateInventoryItemTransferApprovalAsync(BuroInventoryMovementPlan inventoryMovementPlan,
        BuroEmployee sourceEmployee, BuroEmployee approverEmployee, string tag, EnumApprovalCategoryType categoryType)
    {
        var approval = new BuroApproval
        {
            ItemId = Guid.NewGuid().ToString(),
            Tags = new[] { tag },
            CreatedBy = sourceEmployee.UserItemId,
            LastUpdatedBy = sourceEmployee.UserItemId,
            RelatedEntityItemId = inventoryMovementPlan.ItemId,
            RelatedEntityName = nameof(BuroInventoryMovementPlan),
            RequestedFromPersonItemId = sourceEmployee.PersonItemId,
            RequestedFromEmployeeItemId = sourceEmployee.ItemId,
            RequestedFromEmployeePIN = sourceEmployee.EmployeePin,
            RequestedFromEmployeeName = sourceEmployee.EmployeeName,
            ActionedByPersonItemId = approverEmployee.PersonItemId,
            ActionedByEmployeeItemId = approverEmployee.ItemId,
            ActionedByEmployeePIN = approverEmployee.EmployeePin,
            ActionedByEmployeeName = approverEmployee.EmployeeName,
            ActionedByEmployeeDesignation = approverEmployee.DesignationTitle,
            Status = EnumApprovalStatusType.Pending,
            Category = categoryType
        };
        await _repository.InsertOneAsync(approval);

        return approval.ItemId;
    }

    private async Task<BuroEmployee> GetEmployeeByDesignationForAnOfficeAsync(
        string officeItemId,
        string designationCode)
    {
        var designation = await _repository.FindOneAsync<BuroDesignation>(d => d.DesignationCode == designationCode);

        var employees = await _repository.FindOneAsync<BuroEmployee>(
            e => e.CurrentOfficeItemId == officeItemId
                 && e.DesignationItemId == designation.ItemId);

        return employees;
    }

    private static object MakeNotificationPayloadForApprover(
        BuroInventoryMovementPlan inventoryMovementPlan,
        string addedBy,
        string approvalItemId,
        BuroEmployee manager)
    {
        return new
        {
            InventoryMovementPlanItemId = inventoryMovementPlan.ItemId,
            AssignedAt = DateTime.UtcNow,
            AssignedBy = addedBy,
            ApprovalItemId = approvalItemId,
            ApproverName = manager.EmployeeName,
            ApproverPin = manager.EmployeePin,
            ApproverDesignation = manager.DesignationTitle
        };
    }

    private BuroInventoryMovement CreateInventoryMovement(
        BuroInventoryMovementPlan plan,
        int totalQuantity,
        double totalPrice,
        string sourceOfficeItemId,
        string sourceOfficeName,
        string recipientOfficeItemId,
        string recipientOfficeName,
        EnumInventoryMovementType movementType)
    {
        return PopulateInventoryMovement(plan, plan.CreatedBy, totalQuantity, totalPrice, sourceOfficeItemId,
            sourceOfficeName, recipientOfficeItemId, recipientOfficeName, movementType);
    }

    private BuroInventoryMovement PopulateInventoryMovement(BuroInventoryMovementPlan movementPlan,
        string createdBy,
        int totalQuantity,
        double totalPrice,
        string sourceOfficeItemId,
        string sourceOfficeName,
        string recipientOfficeItemId,
        string recipientOfficeName,
        EnumInventoryMovementType movementType)
    {
        var inventoryMovement = new BuroInventoryMovement
        {
            ItemId = Guid.NewGuid().ToString(),
            InventoryItemId = movementPlan.InventoryItemId,
            InventoryName = movementPlan.InventoryName,
            InventoryItemDefinition = movementPlan.InventoryItemDefinition,
            CategoryItemId = movementPlan.CategoryItemId,
            Category = movementPlan.Category,
            SubCategoryItemId = movementPlan.SubCategoryItemId,
            SubCategory = movementPlan.SubCategory,
            VendorItemId = movementPlan.VendorItemId,
            VendorName = movementPlan.VendorName,
            Quantity = totalQuantity,
            HasSerialNumber = movementPlan.HasSerialNumber,
            SerialNumber = string.Join(',', movementPlan.InventoryItemDetails.Select(item => item.SerialNumber)),
            RequiresExpiryDate = movementPlan.RequiresExpiryDate,
            ExpiryDate = movementPlan.InventoryItemDetails.Max(item => item.ExpiryDate),
            TotalPrice = totalPrice,
            MovementType = movementType,
            IsTransferredItem = true,
            SourceOfficeItemId = sourceOfficeItemId,
            SourceOfficeName = sourceOfficeName,
            RecipientOfficeItemId = recipientOfficeItemId,
            RecipientOfficeName = recipientOfficeName,
            Reason = movementPlan.Reason,
            CreatedBy = createdBy,
            CreateDate = DateTime.UtcNow
        };
        return inventoryMovement;
    }

}