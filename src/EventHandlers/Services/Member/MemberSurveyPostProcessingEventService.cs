using EventHandlers.Contracts;
using FinMongoDbRepositories;
using Infrastructure.Constants;
using Infrastructure.Contracts;
using Infrastructure.Events.MemberSurvey;
using Infrastructure.Extensions;
using Microsoft.Extensions.Logging;
using Microsoft.OpenApi.Extensions;
using MongoDB.Bson;
using Selise.Ecap.Entities.PrimaryEntities.PlatformDataService;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Product;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace EventHandlers.Services.Member
{
    public class MemberSurveyPostProcessingEventService : IMemberSurveyPostProcessingEventService
    {
        private readonly ILogger<MemberSurveyPostProcessingEventService> _logger;
        private readonly ISecurityContextProvider _securityContextProvider;
        private readonly IFinMongoDbRepository _finMongoDbRepository;
        private readonly ISharedService _commonService;
        private readonly ITransactionClientService _transactionClientService;

        public MemberSurveyPostProcessingEventService(
            ILogger<MemberSurveyPostProcessingEventService> logger,
            ISecurityContextProvider securityContextProvider,
            IFinMongoDbRepository finMongoDbRepository,
            ISharedService commonService,
            ITransactionClientService transactionClientService)
        {
            _logger = logger;
            _securityContextProvider = securityContextProvider;
            _finMongoDbRepository = finMongoDbRepository;
            _commonService = commonService;
            _transactionClientService = transactionClientService;
        }

        public async Task ProcessMemberSurveyAsync(MemberSurveyPostProcessingEvent command)
        {
            _logger.LogInformation("Member Survey post processing started with survey Id: {SurveyItemId}", command.SurveyItemId);

            switch (command.Status)
            {
                case EnumMemberSurveyStatus.Approved:
                    await ProcessApprovedMemberSurveyAsync(command);
                    break;
                case EnumMemberSurveyStatus.Blacklisted:
                    await MakeMemberInactiveAsync(command);
                    break;
            }

            _logger.LogInformation("Successfully completed Member Survey post processing survey Id: {SurveyItemId}", command.SurveyItemId);
        }

        #region MakeMemberInactiveAsync
        private async Task MakeMemberInactiveAsync(MemberSurveyPostProcessingEvent command)
        {
            var loggedInUser = _securityContextProvider.GetSecurityContext();
            var (memberItemId, personItemId) = await GetLinkedMemberData(command.SurveyItemId);

            if (!string.IsNullOrWhiteSpace(memberItemId))
            {
                await BlacklistMemberAsync(memberItemId, loggedInUser);
            }

            if (!string.IsNullOrWhiteSpace(personItemId))
            {
                // NOSONAR Need to deactivate the user in future
                await DeactivatePersonAsync(personItemId, loggedInUser);
            }
        }

        private async Task<(string MemberItemId, string PersonItemId)> GetLinkedMemberData(string surveyItemId)
        {
            var linkedMember = (await _finMongoDbRepository.FindWithProjectionAsync<BuroMember, dynamic>(
                m => m.LinkedSurveyItemIds.Contains(surveyItemId),
                m => new
                {
                    m.ItemId,
                    m.PersonItemId
                }))
                .FirstOrDefault();

            return (linkedMember?.ItemId, linkedMember?.PersonItemId);
        }

        private async Task DeactivatePersonAsync(string personItemId, SecurityContext loggedInUser)
        {
            var personProperties = new Dictionary<string, object>
            {
                { nameof(Person.LastUpdateDate), DateTime.UtcNow },
                { nameof(Person.LastUpdatedBy), loggedInUser.UserId },
                { nameof(Person.Active), false }
            };

            await _finMongoDbRepository.UpdateOneAsync<Person>(p => p.ItemId == personItemId && p.Active, personProperties);
        }

        private async Task BlacklistMemberAsync(string memberItemId, SecurityContext loggedInUser)
        {
            var memberProperties = new Dictionary<string, object>
            {
                { nameof(BuroMember.LastUpdateDate), DateTime.UtcNow },
                { nameof(BuroMember.LastUpdatedBy), loggedInUser.UserId },
                { nameof(BuroMember.Status), EnumMemberSurveyStatus.Blacklisted }
            };

            await _finMongoDbRepository.UpdateOneAsync<BuroMember>(m => m.ItemId == memberItemId, memberProperties);
        }
        #endregion

        #region ProcessApprovedMemberSurveyAsync
        private async Task ProcessApprovedMemberSurveyAsync(MemberSurveyPostProcessingEvent command)
        {
            _logger.LogInformation("Processing started for Approved Survey with Survey Id: {SurveyItemId}", command.SurveyItemId);

            var memberSurvey = await _finMongoDbRepository.FindOneAsync<BuroMemberSurvey>(s => s.ItemId == command.SurveyItemId);
            var member = await _finMongoDbRepository.FindOneAsync<BuroMember>(m => m.ItemId == memberSurvey.ProposedMemberItemId);

            if (member != null)
            {
                await UpdateExistingMemberAsync(command, memberSurvey, member);
            }
            else
            {
                var linkedSurveys = await GetLinkedSurveys(memberSurvey);

                var newMember = await CreateNewBuroMemberAsync(memberSurvey, linkedSurveys);
                await AssignDefaultGeneralSavingsAccountAsync(newMember);
            }
        }

        private async Task UpdateExistingMemberAsync(
            MemberSurveyPostProcessingEvent command,
            BuroMemberSurvey memberSurvey,
            BuroMember member)
        {
            ArgumentNullException.ThrowIfNull(memberSurvey);
            ArgumentNullException.ThrowIfNull(member);

            _logger.LogInformation("Updating Existing member data for member ID: {ProposedMemberItemId} with Survey Id: {SurveyItemId}",
                memberSurvey.ProposedMemberItemId, command.SurveyItemId);

            var loggedInUser = _securityContextProvider.GetSecurityContext();
            var memberType = await GetMemberType(memberSurvey.ProposedMemberTypeItemId, memberSurvey.MonthlyIncome);

            var properties = PrepareMemberProperties(
                loggedInUser.UserId,
                command,
                memberSurvey,
                member,
                memberType);

            await _finMongoDbRepository.UpdateOneAsync<BuroMember>(x => x.ItemId == memberSurvey.ProposedMemberItemId, properties);

            _logger.LogInformation("Updated existing member data for member ID: {ProposedMemberItemId} with Survey Id: {SurveyItemId}",
                memberSurvey.ProposedMemberItemId, command.SurveyItemId);
        }

        private static Dictionary<string, object> PrepareMemberProperties(
            string userId,
            MemberSurveyPostProcessingEvent command,
            BuroMemberSurvey memberSurvey,
            BuroMember member,
            MemberType memberType)
        {
            var properties = new Dictionary<string, object>
            {
                { nameof(BuroMember.LastUpdateDate), DateTime.UtcNow },
                { nameof(BuroMember.LastUpdatedBy), userId },
                { nameof(BuroMember.SurveyItemId), command.SurveyItemId },
                { nameof(BuroMember.LastSurveyDate), memberSurvey.CreateDate },
                { nameof(BuroMember.MemberName), memberSurvey.Name },
                { nameof(BuroMember.MemberPhotoId), memberSurvey.MemberPhotoId },
                { nameof(BuroMember.MemberSignatureId), memberSurvey.MemberSignatureId },
                { nameof(BuroMember.NIDNumber), memberSurvey.NIDNumber },
                { nameof(BuroMember.SmartCardIdNumber), memberSurvey.SmartCardIdNumber },
                { nameof(BuroMember.OtherIdType), memberSurvey.OtherIdType },
                { nameof(BuroMember.OtherIdNumber), memberSurvey.OtherIdNumber },
                { nameof(BuroMember.FatherName), memberSurvey.FatherName },
                { nameof(BuroMember.FatherNameInBengali), memberSurvey.FatherNameInBengali },
                { nameof(BuroMember.MotherName), memberSurvey.MotherName },
                { nameof(BuroMember.MotherNameInBengali), memberSurvey.MotherNameInBengali },
                { nameof(BuroMember.DateOfBirth), memberSurvey.DateOfBirth },
                { nameof(BuroMember.Gender), memberSurvey.Gender },
                { nameof(BuroMember.MaritalStatus), memberSurvey.MaritalStatus },
            };

            if (memberSurvey.ContactNumbers.Any())
            {
                properties.Add(nameof(BuroMember.ContactNumbers), new BsonArray(memberSurvey.ContactNumbers.Select(c => c.ToBsonDocument())));
            }

            if (memberType != null)
            {
                properties.Add(nameof(BuroMember.MemberType), memberType.ToBsonDocument());
            }

            if (memberSurvey.AdditionalDocumentIds.Any())
            {
                var additionalDocumentIds = GetUpdatedDocumentIds(memberSurvey, member);
                properties.Add(nameof(BuroMember.AdditionalDocumentIds), additionalDocumentIds);
            }

            return properties;
        }

        private static BsonArray GetUpdatedDocumentIds(
            BuroMemberSurvey memberSurvey,
            BuroMember member)
        {
            member.AdditionalDocumentIds ??= new List<string>();
            member.AdditionalDocumentIds.AddRange(memberSurvey.AdditionalDocumentIds);
            var additionalDocumentIds = new BsonArray(member.AdditionalDocumentIds.Select(i => i.ToBsonDocument()));

            return additionalDocumentIds;
        }

        private async Task<List<string>> GetLinkedSurveys(BuroMemberSurvey memberSurvey)
        {
            _logger.LogInformation("Started to find linked surveys inside GetLinkedSurveys with survey Id: {ItemId}", memberSurvey.ItemId);

            var linkedSurveyItemIds = new List<string>
            {
                memberSurvey.ItemId
            };

            if (string.IsNullOrEmpty(memberSurvey.ParentSurveyItemId))
            {
                return linkedSurveyItemIds;
            }

            linkedSurveyItemIds.Add(memberSurvey.ParentSurveyItemId);

            var surveysWithTheSameParent = await _finMongoDbRepository.FindWithProjectionAsync<BuroMemberSurvey, string>(
                s => s.ParentSurveyItemId == memberSurvey.ParentSurveyItemId,
                s => s.ItemId);

            linkedSurveyItemIds.AddRange(surveysWithTheSameParent);

            _logger.LogInformation("{Count} Linked survey found inside GetLinkedSurveys with survey Id: {ItemId}", linkedSurveyItemIds.Count, memberSurvey.ItemId);

            return linkedSurveyItemIds;
        }

        private async Task<BuroMember> CreateNewBuroMemberAsync(BuroMemberSurvey memberSurvey, List<string> linkedSurveyItemIds)
        {
            _logger.LogInformation("Member creation started inside CreateNewBuroMemberAsync with survey Id: {ItemId}", memberSurvey.ItemId);

            var memberSequenceNumber = await _commonService.GetSequenceNumberAsync(BuroConstants.BuroMemberIdentifier, "M");
            var initiator = await GetEmployeeById(memberSurvey.InitiatorEmployeeItemId);
            var branchOffice = await GetOfficeById(initiator?.CurrentOfficeItemId ?? string.Empty);
            var memberType = await GetMemberType(memberSurvey.ProposedMemberTypeItemId, memberSurvey.MonthlyIncome);

            var (personItemId, proposedUserId) = await CreatePersonForMemberAsync(memberSurvey);

            var newMember = new BuroMember
            {
                ItemId = personItemId,
                MemberName = memberSurvey.Name,
                MemberPhotoId = memberSurvey.MemberPhotoId,
                MemberSignatureId = memberSurvey.MemberSignatureId,
                MemberSequenceNumber = memberSequenceNumber,
                NIDNumber = memberSurvey.NIDNumber,
                SmartCardIdNumber = memberSurvey.SmartCardIdNumber,
                OtherIdType = memberSurvey.OtherIdType,
                OtherIdNumber = memberSurvey.OtherIdNumber,
                FatherName = memberSurvey.FatherName,
                FatherNameInBengali = memberSurvey.FatherNameInBengali,
                MotherName = memberSurvey.MotherName,
                MotherNameInBengali = memberSurvey.MotherNameInBengali,
                DateOfBirth = memberSurvey.DateOfBirth.ToUniversalTime(),
                ContactNumbers = memberSurvey.ContactNumbers,
                Gender = memberSurvey.Gender,
                MaritalStatus = memberSurvey.MaritalStatus,
                MemberType = memberType,
                Status = EnumMemberStatus.Active,
                ProgramOrganizerEmployeeItemId = initiator?.ItemId,
                ProgramOrganizerEmployeeName = initiator?.EmployeeName,
                ProgramOrganizerEmployeePin = initiator?.EmployeePin,
                BranchOfficeItemId = branchOffice?.ItemId,
                BranchOfficeCode = branchOffice?.OfficeCode,
                BranchOfficeName = branchOffice?.OfficeName,
                SurveyItemId = memberSurvey.ItemId,
                PersonItemId = personItemId,
                UserItemId = proposedUserId,
                LinkedSurveyItemIds = linkedSurveyItemIds,
                AdditionalDocumentIds = memberSurvey.AdditionalDocumentIds,
                NumberOfGeneralSavingsAccounts = 1,
                Depth = memberSurvey.Depth,
                Hid = memberSurvey.Hid,
                Path = memberSurvey.Path
            };

            AssignAppropriateCenter(newMember, memberSurvey);

            newMember.AddEntityBasicInfo();

            await _finMongoDbRepository.InsertOneAsync(newMember);

            _logger.LogInformation("Successfully created new Member with member Id: {ItemId}", newMember.ItemId);

            return newMember;
        }

        private async Task<BuroOfficeDto> GetOfficeById(string officeItemId)
        {
            if (string.IsNullOrWhiteSpace(officeItemId))
            {
                return null;
            }

            return (await _finMongoDbRepository.FindWithProjectionAsync<BuroOffice, BuroOfficeDto>(
                o => o.ItemId == officeItemId,
                o => new BuroOfficeDto
                {
                    ItemId = o.ItemId,
                    OfficeCode = o.OfficeCode,
                    OfficeName = o.OfficeName,
                    ParentOfficeItemId = o.ParentOfficeItemId,
                })).FirstOrDefault();
        }

        private async Task<BuroEmployee> GetEmployeeById(string employeeItemId)
        {
            return await _finMongoDbRepository.FindOneAsync<BuroEmployee>(e => e.ItemId == employeeItemId);
        }

        private static void AssignAppropriateCenter(BuroMember newMember, BuroMemberSurvey memberSurvey)
        {
            if (string.IsNullOrWhiteSpace(memberSurvey.ProposedCenterItemId)
                || string.IsNullOrWhiteSpace(memberSurvey.ProposedCenterName))
            {
                newMember.CenterItemId = memberSurvey.CenterItemId;
                newMember.CenterName = memberSurvey.CenterName;
            }
            else
            {
                newMember.CenterItemId = memberSurvey.ProposedCenterItemId;
                newMember.CenterName = memberSurvey.ProposedCenterName;
            }
        }

        private async Task AssignDefaultGeneralSavingsAccountAsync(BuroMember member)
        {
            _logger.LogInformation("Default GS account creation started for member Id: {ItemId}", member.ItemId);

            var accountCode = GenerateAbbreviation(nameof(EnumProductType.GeneralSaving));
            var accountSequenceNumber = await _commonService.GetSequenceNumberAsync(BuroConstants.BuroGeneralSavingsAccountIdentifier, accountCode);
            var defaultGSProductLine = await GetGeneralSavingProductLine();
            var center = await GetCenterAsync(member.CenterItemId);
            var adjustedInterestRate = CalculateAdjustedInterestRate(defaultGSProductLine, member);

            var newAccount = new BuroGeneralSavingsAccount
            {
                AccountCode = accountCode,
                AccountSequenceNumber = accountSequenceNumber,
                AccountStatus = EnumProductAccountStatus.Active,
                OpeningBranchItemId = member.BranchOfficeItemId,
                OpeningBranchName = member.BranchOfficeName,
                CurrentBranchItemId = member.BranchOfficeItemId,
                CurrentBranchName = member.BranchOfficeName,
                OpeningDate = DateTime.UtcNow,
                InterestRate = adjustedInterestRate,
                MemberItemId = member.ItemId,
                PersonItemId = member.PersonItemId,
                ProductLineItemId = defaultGSProductLine.ItemId,
                ProductLineName = defaultGSProductLine.LineName,
                ProgramOrganizerEmployeeItemId = center?.ProgramOrganizerEmployeeItemId ?? string.Empty,
                ProgramOrganizerEmployeeName = center?.ProgramOrganizerEmployeeName ?? string.Empty,
                MemberName = member.MemberName,
                MemberSequenceNumber = member.MemberSequenceNumber,
                MemberBranchOfficeItemId = member.BranchOfficeItemId,
                MemberBranchOfficeName = member.BranchOfficeName,
            };

            newAccount.AddEntityBasicInfo();

            var ressult = await _transactionClientService.CreateAccountAsync(new Infrastructure.Models.Transaction.CreateAccountCommand
            {
                AccountNumber = newAccount.AccountSequenceNumber,
                AccountHolderNumber = newAccount.MemberSequenceNumber,
                AccountName = EnumProductType.GeneralSaving.GetDisplayName(),
                InitialBalance = 0,
                AccountType = TransactionServiceConstrants.AccountTypeConstants.Savings,
            });

            if (!ressult.IsSuccess())
            {
                _logger.LogError("Failed to create account in transaction service for member Id: {ItemId}", member.ItemId);

                throw new InvalidOperationException("Failed to create account in transaction service.");
            }

            await _finMongoDbRepository.InsertOneAsync(newAccount);

            _logger.LogInformation("Default GS account created for member Id: {ItemId} with account id: {AccountItemId}", member.ItemId, newAccount.ItemId);
        }

        private static double CalculateAdjustedInterestRate(GeneralSavingProductLine productLine, BuroMember member)
        {
            var baseInterestRate = productLine.InterestRate;

            if (productLine.AreaSpecificInterests == null || !productLine.AreaSpecificInterests.Any())
                return baseInterestRate;

            foreach (var areaInterest in productLine.AreaSpecificInterests)
            {
                if (areaInterest.CenterSamityItemIds.Contains(member.CenterItemId) ||
                    areaInterest.BranchItemIds.Contains(member.BranchOfficeItemId))
                {
                    return areaInterest.AdjustmentType switch
                    {
                        InterestAdjustmentType.Increment => baseInterestRate + areaInterest.AdjustmentValue,
                        InterestAdjustmentType.Decrement => baseInterestRate - areaInterest.AdjustmentValue,
                        _ => baseInterestRate
                    };
                }
            }

            return baseInterestRate;
        }

        private async Task<BuroCenter> GetCenterAsync(string centerItemId)
        {
            return await _finMongoDbRepository.FindOneAsync<BuroCenter>(c => c.ItemId == centerItemId);
        }

        private async Task<GeneralSavingProductLine> GetGeneralSavingProductLine()
        {
            return await _finMongoDbRepository.FindOneAsync<GeneralSavingProductLine>(pl => pl.DefaultLine)
                ?? throw new InvalidOperationException(string.Format(BuroErrorMessageKeys.RequiredDataNotFound, nameof(GeneralSavingProductLine)));
        }

        private static string GenerateAbbreviation(string camelCase)
        {
            if (string.IsNullOrEmpty(camelCase))
            {
                return string.Empty;
            }

            var capitalLatters = camelCase.Where(char.IsUpper);

            return capitalLatters.Any() ? string.Concat(camelCase.Where(char.IsUpper)) : string.Empty;
        }

        private async Task<(string personId, string proposedUserId)> CreatePersonForMemberAsync(BuroMemberSurvey memberSurvey)
        {
            var loggedInUser = _securityContextProvider.GetSecurityContext();
            var personItemId = memberSurvey.ProposedMemberItemId;
            var userPersonId = memberSurvey.ProposedUserItemId;

            var person = new Person
            {
                ItemId = personItemId,
                ProposedUserId = userPersonId,
                CreatedBy = userPersonId,
                Language = "en-US",
                FirstName = memberSurvey.Name,
                DisplayName = memberSurvey.Name,
                DateOfBirth = memberSurvey.DateOfBirth.ToUniversalTime(),
                Country = "Bangladesh",
                MobileCountryCode = "+880",
                PhoneNumber = memberSurvey.ContactNumbers.Any() ? memberSurvey.ContactNumbers[0] : string.Empty,
                ClientStatus = "REQUEST_SENT",
                CustomerId = loggedInUser.UserId,
                Tags = new[] { "Is-A-ProposedMemberPerson", "Person" },
                IdsAllowedToRead = new[] { loggedInUser.UserId, personItemId },
                IdsAllowedToUpdate = new[] { loggedInUser.UserId, personItemId },
                RolesAllowedToRead = new[] { UserRoles.Admin, UserRoles.AppUser },
                RolesAllowedToUpdate = new[] { UserRoles.Admin, UserRoles.AppUser }
            };

            person.AddEntityBasicInfo();

            await _finMongoDbRepository.InsertOneAsync(person);

            return (personItemId, userPersonId);
        }

        private async Task<MemberType> GetMemberType(string memberTypeItemId, double monthlyIncome)
        {
            var memberConfiguration = await _finMongoDbRepository.FindOneAsync<BuroMemberConfiguration>(c => c.MemberTypes != null && c.MemberTypes.Count > 0);

            if (memberConfiguration == null)
            {
                return default!;
            }

            if (!string.IsNullOrWhiteSpace(memberTypeItemId))
            {
                return memberConfiguration?.MemberTypes.FirstOrDefault(t => t.ItemId == memberTypeItemId);
            }

            var memberType = memberConfiguration?.MemberTypes
                .FirstOrDefault(t => monthlyIncome.CompareTo(t.MinimumMonthlyIncome) >= 0
                                  && monthlyIncome.CompareTo(t.MaximumMonthlyIncome) <= 0);

            return memberType;
        }
        #endregion
    }

    public class BuroOfficeDto
    {
        public string ItemId { get; set; }
        public string OfficeCode { get; set; }
        public string OfficeName { get; set; }
        public string ParentOfficeItemId { get; set; }
    }
}
