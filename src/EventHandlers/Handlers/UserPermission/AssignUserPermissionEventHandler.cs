using EventHandlers.Services.UserPermission;
using Infrastructure.Events.UserPermission;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace EventHandlers.UserPermission;

public class AssignUserPermissionEventHandler : ICommandHandler<AssignUserPermissionEvent, CommandResponse>
{
    private readonly ILogger<AssignUserPermissionEventHandler> _logger;
    private readonly AssignUserPermissionService _userPermissionService;

    public AssignUserPermissionEventHandler(ILogger<AssignUserPermissionEventHandler> logger,
        AssignUserPermissionService userPermissionService)
    {
        _logger = logger;
        _userPermissionService = userPermissionService;
    }
    public CommandResponse Handle(AssignUserPermissionEvent command)
    {
        throw new NotImplementedException();
    }

    public async Task<CommandResponse> HandleAsync(AssignUserPermissionEvent command)
    {
        var commandResponse = new CommandResponse();
        try
        {
            _logger.LogInformation("Inside AssignUserPermissionEventHandler :: command :: {command}", JsonConvert.SerializeObject(command));
            commandResponse = await _userPermissionService.AssignUserPermission(command);

        }
        catch (Exception e)
        {
            _logger.LogError("{StackTrace}", e.StackTrace);
        }
        return commandResponse;
    }
}