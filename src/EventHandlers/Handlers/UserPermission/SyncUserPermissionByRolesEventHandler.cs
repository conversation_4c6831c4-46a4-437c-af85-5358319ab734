using EventHandlers.Services.UserPermission;
using Infrastructure.Events.UserPermission;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace EventHandlers.UserPermission;

public class SyncUserPermissionByRolesEventHandler : ICommandHandler<SyncUserPermissionByRolesEvent, CommandResponse>
{
    private readonly ILogger<SyncUserPermissionByRolesEventHandler> _logger;
    private readonly SyncUserPermissionByRolesService _syncUserPermissionByRolesService;

    public SyncUserPermissionByRolesEventHandler(ILogger<SyncUserPermissionByRolesEventHandler> logger,
        SyncUserPermissionByRolesService syncUserPermissionByRolesService
        )
    {
        _logger = logger;
        _syncUserPermissionByRolesService = syncUserPermissionByRolesService;
    }
    public CommandResponse Handle(SyncUserPermissionByRolesEvent command)
    {
        throw new NotImplementedException();
    }

    public async Task<CommandResponse> HandleAsync(SyncUserPermissionByRolesEvent command)
    {
        var commandResponse = new CommandResponse();
        try
        {
            _logger.LogInformation("Inside SyncUserPermissionByRolesEventHandler :: command :: {command}", JsonConvert.SerializeObject(command));
            commandResponse = await _syncUserPermissionByRolesService.SyncUserPermissionByRoles(command);
        }
        catch (Exception e)
        {
            _logger.LogError("{StackTrace}", e.StackTrace);
        }
        return commandResponse;
    }
}