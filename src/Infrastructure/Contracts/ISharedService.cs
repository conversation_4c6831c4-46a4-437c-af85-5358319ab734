using Infrastructure.Models;

namespace EventHandlers.Contracts
{
    public interface ISharedService
    {
        Task<string> GetSequenceNumberAsync(string context, string prefix);
        Task NotifyUserAsync(string responseKey, object notificationPayload, string entityItemId, string userId);
        string GenerateAbbreviation(string camelCasedStr);
        Task<EmployeeIdentityDto> GetEmployeeInfoAsync(string userId);
    }
}
