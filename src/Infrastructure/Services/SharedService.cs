using EventHandlers.Contracts;
using FinMongoDbRepositories;
using Infrastructure.Contracts;
using Infrastructure.Enums;
using Infrastructure.Models;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Entities.PrimaryEntities.BURO;

namespace EventHandlers.Services
{
    public class SharedService : ISharedService
    {
        private readonly ILogger<SharedService> _logger;
        private readonly ISequenceNumberClient _sequenceNumberClient;
        private readonly INotificationServiceClient _notificationServiceClient;
        private readonly IFinMongoDbRepository _finMongoDbRepository;

        public SharedService(
            ILogger<SharedService> logger,
            ISequenceNumberClient sequenceNumberClient,
            INotificationServiceClient notificationServiceClient,
            IFinMongoDbRepository finMongoDbRepository)
        {
            _logger = logger;
            _sequenceNumberClient = sequenceNumberClient;
            _notificationServiceClient = notificationServiceClient;
            _finMongoDbRepository = finMongoDbRepository;
        }

        public async Task<string> GetSequenceNumberAsync(string context, string prefix)
        {
            var sequence = await _sequenceNumberClient.GetSequenceNumber(new SequenceNumberQuery { Context = context });

            if (sequence == null || sequence.CurrentNumber <= 0)
            {
                _logger.LogWarning("Invalid sequence number received from the sequence number service for context: {Context}", context);

                sequence = new SequenceNumberQueryResponse
                {
                    Context = context,
                    CurrentNumber = DateTime.UtcNow.Ticks
                };
            }

            var sequenceNumber = sequence.CurrentNumber.ToString().PadLeft(6, '0');

            return $"{prefix}{sequenceNumber}";
        }

        public async Task NotifyUserAsync(
            string responseKey,
            object notificationPayload,
            string entityItemId,
            string userId)
        {
            _logger.LogInformation("In NotifyUserAsync");

            try
            {
                var payloadWithResponse = MakeNotifierPayloadWithResponse(responseKey, notificationPayload, userId);

                await _notificationServiceClient.NotifyAsync(payloadWithResponse);

                _logger.LogInformation("Notification sent for Entity ID: {EntityItemId} to User ID: {UserId}", entityItemId, userId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send notification for Entity ID: {EntityItemId} to User ID: {UserId}", entityItemId, userId);
            }
        }

        private NotifierPayloadWithResponse MakeNotifierPayloadWithResponse(
            string ResponseKey,
            object notificationPayload,
            params string[] userId)
        {
            _logger.LogInformation("In MakeNotifierPayloadWithResponse");

            var response = new NotifierPayloadWithResponse
            {
                UserIds = userId.Select(i => Guid.Parse(i)).ToList(),
                NotificationType = userId.Length == 1
                    ? NotificationReceiverTypes.UserSpecificReceiverType
                    : NotificationReceiverTypes.BroadcastReceiverType,
                ResponseKey = ResponseKey,
                DenormalizedPayload = JsonConvert.SerializeObject(notificationPayload)
            };

            return response;
        }

        public string GenerateAbbreviation(string camelCasedStr)
        {
            if (string.IsNullOrWhiteSpace(camelCasedStr))
            {
                return string.Empty;
            }

            var capitalLatters = camelCasedStr.Where(char.IsUpper);

            return capitalLatters.Any() ? string.Concat(capitalLatters) : string.Empty;
        }
        
        public async Task<EmployeeIdentityDto> GetEmployeeInfoAsync(string userId)
        {
            var employeeInfo = await _finMongoDbRepository.FindWithProjectionAsync<BuroEmployee, EmployeeIdentityDto>(
                e => e.UserItemId == userId,
                e => new EmployeeIdentityDto
                {
                    EmployeeName = e.EmployeeName,
                    EmployeePin = e.EmployeePin,
                    DesignationTitle = e.DesignationTitle,
                    CurrentOfficeTitle = e.CurrentOfficeTitle,
                    CurrentOfficeCode = e.CurrentOfficeCode,
                    UserItemId = e.UserItemId,
                    EmployeeItemId = e.ItemId
                });

            return employeeInfo[0];
        }
    }
}
