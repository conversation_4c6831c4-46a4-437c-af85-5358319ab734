using FinMongoDbRepositories;
using Infrastructure.Constants;
using Infrastructure.Contracts;
using Infrastructure.Contracts.Transactions;
using Infrastructure.Events.Transaction;
using Infrastructure.Extensions;
using Infrastructure.Models.Transaction;
using Microsoft.Extensions.Logging;
using Microsoft.OpenApi.Extensions;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Infrastructure.Services.Transactions
{
    public class ProductAccountTransactionService : IProductAccountTransactionService
    {
        private readonly ILogger<ProductAccountTransactionService> _logger;
        private readonly IFinMongoDbRepository _finMongoDbRepository;
        private readonly ITransactionContextFactoryResolverService _transactionContextFactoryResolverService;
        private readonly ITransactionClientService _transactionClientService;
        private readonly IServiceClient _serviceClient;

        public ProductAccountTransactionService(
            ILogger<ProductAccountTransactionService> logger,
            IFinMongoDbRepository finMongoDbRepository,
            ITransactionContextFactoryResolverService transactionContextFactoryResolverService,
            ITransactionClientService transactionClientService,
            IServiceClient serviceClient)
        {
            _logger = logger;
            _finMongoDbRepository = finMongoDbRepository;
            _transactionContextFactoryResolverService = transactionContextFactoryResolverService;
            _transactionClientService = transactionClientService;
            _serviceClient = serviceClient;
        }

        public async Task ExecuteAsync(string productAccountTransactionItemId)
        {
            _logger.LogInformation("Starting product account transaction execution for transaction: {TransactionItemId}", productAccountTransactionItemId);

            var transactionRequest = await GetTransactionRequestByIdAsync(productAccountTransactionItemId);

            var transactionContextFactory = _transactionContextFactoryResolverService.GetFactory(transactionRequest.ProductType);

            var accountTransactionService = transactionContextFactory.GetAccountTransactionService(transactionRequest.TransactionType);
            var productAccountService = transactionContextFactory.GetProductAccountService();
            var paymentProcessorService = transactionContextFactory.GetPaymentProcessorService(transactionRequest.TransactionMedium);

            // NOSONAR we should not initiate tran here as there can be grouped transaction
            await InitiateProductAccountTransactionAsync(transactionRequest);

            await productAccountService.ExecuteTransactionAsync(accountTransactionService, productAccountTransactionItemId);
            await paymentProcessorService.ExecutePaymentAsync();

            // NOSOANR we should not update track the payment here as there can be transactions by system user
            SentToTrackAccountPaymentEvent(transactionRequest);

            _logger.LogInformation("Product account transaction execution completed for transaction: {TransactionItemId}", productAccountTransactionItemId);
        }

        private void SentToTrackAccountPaymentEvent(BuroProductAccountTransactionRequest transaction)
        {
            _logger.LogInformation("Sending TrackAccountPaymentEvent for transaction: {TransactionId} Amount: {Amount}", transaction.ItemId, transaction.Amount);

            var trackPayementEvent = new TrackAccountPaymentEvent
            {
                AccountPayments = new List<AccountPayment>
                {
                    new()
                    {
                        MemberItemId = transaction.MemberItemId,
                        AccountItemId = transaction.AccountItemId,
                        PaidAmount = transaction.Amount,
                        ProductType = transaction.ProductType,
                    }
                }
            };

            _serviceClient.SendToQueue<CommandResponse>(QueueNames.BuroCommandQueue, trackPayementEvent);

            _logger.LogInformation("TrackAccountPaymentEvent sent successfully for transaction: {TransactionId}", transaction.ItemId);
        }

        #region Private
        private async Task InitiateProductAccountTransactionAsync(BuroProductAccountTransactionRequest transactionRequest)
        {
            var response = await _transactionClientService.InitiateTransactionAsync(new InitiateTransactionCommand
            {
                Transactions = new List<TransactionCommand>
                {
                    new()
                    {
                        AccountHolderNumber = transactionRequest.MemberSequenceNumber,
                        AccountNumber = transactionRequest.AccountSequenceNumber,
                        Amount = transactionRequest.Amount,
                        TransactionType = ResolveTransactionTypeConstantForTransactionService(transactionRequest),
                        AccountType = ResolveAccountTypeConstantForTransactionService(transactionRequest),
                        Reference = transactionRequest.TransactionType.GetDisplayName(),
                    }
                },
                MessageCorrelationId = Guid.NewGuid().ToString(),
            });

            if (!response.IsSuccess())
            {
                _logger.LogError("Trnsaction failed for MemberItemId: {MemberItemId}", transactionRequest.MemberItemId);

                throw new InvalidOperationException($"Transactions failed for payment");
            }
        }

        private static int ResolveAccountTypeConstantForTransactionService(BuroProductAccountTransactionRequest transactionRequest)
        {
            return transactionRequest.ProductType switch
            {
                EnumProductType.GeneralSaving => TransactionServiceConstrants.AccountTypeConstants.Savings,
                EnumProductType.ContractualSaving => TransactionServiceConstrants.AccountTypeConstants.Current,
                EnumProductType.Loan => TransactionServiceConstrants.AccountTypeConstants.Loan,
                _ => default
            };
        }

        private static int ResolveTransactionTypeConstantForTransactionService(BuroProductAccountTransactionRequest transactionRequest)
        {
            return transactionRequest.TransactionType switch
            {
                EnumBuroTransactionType.GsDeposit => TransactionServiceConstrants.TransactionTypeConstants.Deposit,
                EnumBuroTransactionType.GsWithdrawal => TransactionServiceConstrants.TransactionTypeConstants.Withdrawal,
                EnumBuroTransactionType.CsWithdrawalMaturity => TransactionServiceConstrants.TransactionTypeConstants.Withdrawal,
                _ => default
            };
        }

        private async Task<BuroProductAccountTransactionRequest> GetTransactionRequestByIdAsync(string productAccountTransactionItemId)
        {
            return await _finMongoDbRepository.FindOneAsync<BuroProductAccountTransactionRequest>(t => t.ItemId == productAccountTransactionItemId)
                ?? throw new InvalidOperationException(string.Format(BuroErrorMessageKeys.RequiredDataNotFound, nameof(BuroProductAccountTransactionRequest)));
        } 
        #endregion
    }
}
