using Infrastructure.Contracts;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;

namespace Infrastructure.Events.Voucher;

public class CreateVoucherEvent : BaseEvent, IVoucherEvent
{
    public string VoucherCode { get; set; } = default!;
    public string VoucherGroupItemId { get; set; } = default!;
    public string OfficeItemId { get; set; } = default!;
    public string RelatedEntityName { get; set; } = default!;
    public string RelatedEntityItemId { get; set; } = default!;
    public string TransactionRefNo { get; set; } = default!;
    public EnumVoucherScopeCategory VoucherScopeCategory { get; set; }
    public double? Amount { get; set; } = default!;
    public string Narration { get; set; } = default!;
    public string TransactionTypeTag { get; set; } = default!;
    public string VoucherConfigItemId { get; set; } = default!;
    public string ProductLineItemId { get; set; } = default!;
}