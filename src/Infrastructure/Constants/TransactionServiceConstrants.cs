namespace Infrastructure.Constants
{
    public static class TransactionServiceConstrants
    {
        public const string TokenSchema = "Bearer";
        public const string TransactionServiceName = "TransactionService";

        public static class TransactionEndpoints
        {
            public const string CreateAccount = "AccountCommand/Create";
            public const string UpdateAccount = "AccountCommand/Update";
            public const string DeleteAccount = "AccountCommand/Delete";
            public const string InitiateTransaction = "TransactionCommand/Initiate";
            public const string ReverseTransaction = "TransactionCommand/Reverse";
        }

        public static class AccountTypeConstants
        {
            public const int Savings = 0;
            public const int Loan = 1;
            public const int Investment = 2;
            public const int Current = 3;
        }

        public static class TransactionTypeConstants
        {
            public const int Deposit = 1;
            public const int Withdrawal = 2;
        }
    }
}
