namespace Infrastructure.Constants
{
    public static class BuroNotificationKeys
    {
        public const string EmployeeStaffingRequestCreated = "Employee.Staffing.RequestCreated";
        public const string BuroCenterCreationApprovalCreated = "Buro.Center.Creation.Approval.Created";
        public const string MemberPhoneNumberChangeApprovalCreated = "Member.Phone.Number.Change.Approval.Created";
        public const string MemberAddressChangeApprovalCreated = "Member.Address.Change.Approval.Created";
        public const string ConsentFormBranchManagerApprovalCreated = "Consent.Form.BranchManager.Created";
        public const string CommentedInRegister = "Register.Commented";

        public const string ContractualAccountBranchManagerInvited = "Contractual.Account.BranchManager.Invited";
        public const string ContractualAccountBranchManagerApproved = "Contractual.Account.BranchManager.Approved";
        public const string ContractualAccountBranchManagerRejected = "Contractual.Account.BranchManager.Rejected";

        public const string UserPermissionUpdated = "UserPermission.Updated";

        public const string GSAmountDeposited = "GS.Amount.Deposited";
        public const string GSAmountWithdrawal = "GS.Amount.Withdrawal";
        public const string GSAccountChangeBranchManagerInvited = "GS.Account.Change.BranchManager.Invited";
        public const string GSAccountChangeRequestApproved = "GS.Account.Change.Request.BranchManager.Approved";
        public const string GSAccountChangeRequestRejected = "GS.Account.Change.Request.BranchManager.Rejected";

        public const string CollectionFundTransferCompleted = "Collection.Fund.Transfer.Completed";
        public const string CollectionFundTransferRevertBranchManagerInvited = "Collection.Fund.Transfer.Revert.BranchManager.Invited";
        public const string CollectionFundTransferRevertBranchManagerRejected = "Collection.Fund.Transfer.Revert.BranchManager.Rejected";
        public const string CollectionFundTransferRevertBranchManagerReverted = "Collection.Fund.Transfer.Revert.BranchManager.Reverted";
        public const string GSAccountWithdrawalBranchManagerInvited = "GS.Account.Withdrawal.BranchManager.Invited";
        public const string CSAccountWithdrawalBranchManagerInvited = "CS.Account.Withdrawal.BranchManager.Invited";

        public const string InventoryOutItemBranchManagerInvited = "Inventory.Out.Item.BranchManager.Invited";
        public const string InventoryTransferItemBranchManagerInvited = "Inventory.Transfer.Item.BranchManager.Invited";
        public const string InventoryRequestRejected = "Inventory.Request.Rejected";

        // NOSONAR Please Don't make the property read-only as it can be used to add new keys dynamically
        public static HashSet<string> OfflineNotificationResponseKeys => new()
        {
            EmployeeStaffingRequestCreated,
            BuroCenterCreationApprovalCreated,
            MemberPhoneNumberChangeApprovalCreated,
            MemberAddressChangeApprovalCreated,
            ContractualAccountBranchManagerInvited,
            ContractualAccountBranchManagerApproved,
            ContractualAccountBranchManagerRejected,
            GSAmountDeposited,
            GSAmountWithdrawal,
            GSAccountChangeBranchManagerInvited,
            GSAccountChangeRequestApproved,
            GSAccountChangeRequestRejected,
            CollectionFundTransferCompleted,
            CollectionFundTransferRevertBranchManagerInvited,
            CollectionFundTransferRevertBranchManagerRejected,
            CollectionFundTransferRevertBranchManagerReverted,
            GSAccountWithdrawalBranchManagerInvited,
            CSAccountWithdrawalBranchManagerInvited
        };
    }
}
