using Infrastructure.Contracts;
using Infrastructure.Contracts.Retry;
using Infrastructure.Contracts.Transactions;
using Infrastructure.Services;
using Infrastructure.Services.RetryServices;
using Infrastructure.Services.Transactions;
using Microsoft.Extensions.DependencyInjection;
using SeliseBlocks.Genesis.Framework.Infrastructure;
using SeliseBlocks.MailService.Driver;
using SeliseBlocks.Uam.Driver;

namespace ServiceCollector.Infrastructure;

public static class InfrastructureServiceCollector
{
    public static void AddInfrastructureService(this IServiceCollection serviceCollection, IAppSettings appSettings)
    {
        serviceCollection.AddSingleton<IMailServiceClient, MailServiceClient>();
        serviceCollection.AddSingleton<IBuroKeyStore, BuroKeyStore>();
        serviceCollection.AddSingleton<IBusinessConfig, BusinessConfig>();
        serviceCollection.AddSingleton<IAccessTokenProvider, BusinessAccessTokenProvider>();
        serviceCollection.AddSingleton<IBusinessHttpClient, BusinessHttpClient>();
        serviceCollection.AddSingleton<IUamAdapter, UamAdapter>();
        serviceCollection.AddSingleton<ISmsServiceClient, SmsServiceClient>();
        serviceCollection.AddSingleton<IGoogleCaptchaVerificationService, GoogleCaptchaVerificationService>();
        serviceCollection.AddSingleton<ISequenceNumberClient, SequenceNumberClientService>();
        serviceCollection.AddSingleton<IUamServiceClient, UamServiceClient>();
        serviceCollection.AddSingleton<IChangeLogRepository, ChangeLogRepository>();
        serviceCollection.AddSingleton<IChangeLogService, ChangeLogService>();
        serviceCollection.AddSingleton<IBusinessRepository, BusinessRepository>();
        serviceCollection.AddSingleton<INotificationServiceClient, NotificationServiceClient>();
        serviceCollection.AddSingleton<ITransactionClientService, TransactionClientService>();
        serviceCollection.AddSingleton<IAccountPaymentPostProcessor, CSAccountPostProcessorService>();
        serviceCollection.AddSingleton<IAccountPaymentPostProcessor, LoanAccountPostProcessorService>();
        serviceCollection.AddSingleton<IRetryStrategy, RetryStrategy>();
        serviceCollection.AddSingleton<IRetryExecutorService, RetryExecutorService>();
    }
}