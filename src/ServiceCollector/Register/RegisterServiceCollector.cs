
using Domain.Contracts.Register;
using Domain.Services.Register;
using Microsoft.Extensions.DependencyInjection;


namespace ServiceCollector.Register;

public static class RegisterServiceCollector
{
    public static void AddRegisterServices(this IServiceCollection serviceCollection)
    {
        serviceCollection.AddSingleton<IChequeReturnService, ReturnChequeService>();
        serviceCollection.AddSingleton<ICreateIssueChequeRegisterCommandService, CreateIssueChequeRegisterCommandService>();
        serviceCollection.AddSingleton<ICreatePostRegisterCommandService, CreatePostRegisterCommandService>();

    }
}