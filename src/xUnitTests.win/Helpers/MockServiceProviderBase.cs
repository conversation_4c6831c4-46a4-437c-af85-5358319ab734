using FinMongoDbRepositories;
using Infrastructure.Contracts;
using Infrastructure.Models;
using Microsoft.Extensions.Logging;
using MongoDB.Driver;
using Moq;
using NPOI.SS.Formula.Functions;
using Org.BouncyCastle.Asn1.Pkcs;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Genesis.Framework.Infrastructure;
using System.Linq.Expressions;

namespace xUnitTests.win.Helpers
{
    public abstract class MockServiceProviderBase<TService>
    {
        protected Mock<IRepository> MockRepository { get; }
        protected Mock<IFinMongoDbRepository> MockFinMongoDbRepository { get; }
        protected Mock<ISecurityContextProvider> MockSecurityContextProvider { get; }
        protected Mock<ILogger<TService>> MockLogger { get; }
        protected Mock<IServiceProvider> MockServiceProvider { get; }
        protected Mock<INotificationServiceClient> MockNotificationServiceClient { get; }

        protected MockServiceProviderBase()
        {
            MockRepository = new Mock<IRepository>();
            MockFinMongoDbRepository = new Mock<IFinMongoDbRepository>();
            MockSecurityContextProvider = new Mock<ISecurityContextProvider>();
            MockLogger = new Mock<ILogger<TService>>();
            MockServiceProvider = new Mock<IServiceProvider>();
            MockNotificationServiceClient = new Mock<INotificationServiceClient>();
        }

        protected void SetupServiceProvide<T>() where T : class
        {
            MockServiceProvider
                .Setup(sp => sp.GetService(typeof(T)))
                .Returns(typeof(T));
        }

        protected void SetupRepositoryGetItemAsync<T>(List<T> mockData) where T : class
        {
            MockRepository.Setup(repo => repo.GetItemAsync(It.IsAny<Expression<Func<T, bool>>>()))
                .Returns((Expression<Func<T, bool>> filter) => Task.FromResult(mockData.AsQueryable().FirstOrDefault(filter) ?? default!));
        }

        protected void SetupRepositoryGetItems<T>(List<T> mockData) where T : class
        {
            MockRepository
                .Setup(repo => repo.GetItems(It.IsAny<Expression<Func<T, bool>>>()))
                .Returns((Expression<Func<T, bool>> filter) => mockData.AsQueryable().Where(filter).AsQueryable());
        }

        protected void SetupRepositoryExistsAsync<T>(List<T> mockData) where T : class
        {
            MockRepository.Setup(repo => repo.ExistsAsync(It.IsAny<Expression<Func<T, bool>>>()))
                .ReturnsAsync((Expression<Func<T, bool>> filter) => mockData.AsQueryable().Any(filter));
        }


        protected void SetupRepositorySaveAsync()
        {
            MockRepository.Setup(repo => repo.SaveAsync(It.IsAny<object>(), It.IsAny<string>()))
                .Returns(Task.CompletedTask);
        }

        protected void SetupRepositoryUpdateAsync<T>() where T : class
        {
            MockRepository.Setup(repo => repo.UpdateAsync(It.IsAny<Expression<Func<T, bool>>>(), It.IsAny<IDictionary<string, object>>()))
                .Returns(Task.CompletedTask);
        }

        protected void SetupFinMongoDbRepositoryFindWithOptionAsync<T>(List<T> mockData) where T : class
        {
            MockFinMongoDbRepository
                .Setup(repo => repo.FindWithOptionAsync(It.IsAny<FilterDefinition<T>>(), It.IsAny<FindOptions<T, T>>(), true))
                .ReturnsAsync(mockData);
        }

        protected void SetupFinMongoDbRepositoryFindAsync<T>(List<T> mockData) where T : class
        {
            MockFinMongoDbRepository
            .Setup(repo => repo.FindAsync(It.IsAny<Expression<Func<T, bool>>>(), It.IsAny<FindOptions>(), true))
            .ReturnsAsync(mockData);
        }

        protected void SetupFinMongoDbRepositoryGetCountByFilterDefinitionAsync<T>(int count) where T : class
        {
            MockFinMongoDbRepository
                .Setup(repo => repo.GetCountByFilterDefinitionAsync(It.IsAny<FilterDefinition<T>>(), true))
                .ReturnsAsync(count);
        }

        protected void SetupMockSecurityContextProvider(string userId = "user_id_01", IEnumerable<string>? roles = null)
        {
            var securityContext = CreateMockSecurityContext(userId, roles ?? new List<string> { "Role1", "Role2" });

            MockSecurityContextProvider
                .Setup(provider => provider.GetSecurityContext())
                .Returns(securityContext);
        }

        protected void SetupMockNotificationServiceClient()
        {
            MockNotificationServiceClient
                .Setup(client => client.NotifyAsync(It.IsAny<NotifierPayloadWithResponse>()))
                .ReturnsAsync(new HttpResponseMessage(System.Net.HttpStatusCode.OK));
        }

        private static SecurityContext CreateMockSecurityContext(string userId, IEnumerable<string> roles)
        {
            return new SecurityContext(
                userName: "testUser",
                roles: roles.ToArray(),
                tenantId: "tenant-123",
                oauthBearerToken: "sample_oauth_bearer_token",
                isAuthenticated: true,
                isUserAuthenticated: true,
                displayName: "Delower Hosen",
                userId: userId,
                requestOrigin: "burobd.seliselocal.com",
                siteName: "burobd.seliselocal.com",
                siteId: "site-123",
                email: "<EMAIL>",
                language: "en",
                phoneNumber: "123456789",
                sessionId: "session-123",
                requestUri: new Uri("https://test.com"),
                serviceVersion: "v1",
                hasDynamicRoles: false,
                tokenHijackingProtectionHash: string.Empty,
                userAutoExpire: false,
                userExpireOn: DateTime.UtcNow.AddHours(1),
                userPrefferedLanguage: "en",
                postLogOutHandlerDataKey: string.Empty,
                organizationId: "org-123"
            );
        }
    }
}