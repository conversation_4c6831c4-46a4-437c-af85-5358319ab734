using Domain.Commands.Account;
using Domain.Services.Account;
using Microsoft.Extensions.Logging;
using Moq;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Product;
using xUnitTests.Helpers;

namespace xUnitTests.Services.Account
{
    public class SubmitLoanCommandServiceTests : BaseTest
    {
        private readonly string _userItemId;
        private readonly string _loanApplicationItemId;
        private readonly string _loanProductLineItemId;
        private readonly string _tenureId;
        private readonly string _memberItemId;

        private readonly SubmitLoanCommandService _service;
        private readonly ILogger<SubmitLoanCommandService> _logger;

        public SubmitLoanCommandServiceTests()
        {
            _userItemId = Guid.NewGuid().ToString();
            _loanApplicationItemId = Guid.NewGuid().ToString();
            _loanProductLineItemId = Guid.NewGuid().ToString();
            _tenureId = Guid.NewGuid().ToString();
            _memberItemId = Guid.NewGuid().ToString();

            _logger = Mock.Of<ILogger<SubmitLoanCommandService>>();

            SetupMockSecurityContext(_userItemId);
            SetupAdditionalMockServices();

            _service = new SubmitLoanCommandService(
                _logger,
                _mockRepository.Object,
                _mockSecurityContextProvider.Object,
                _mockServiceClient.Object);

            SeedMockData();
        }

        private void SeedMockData()
        {
            SeedBuroLoanAccountData();
            SeedBuroLoanApplicationData();
            SeedLoanProductLineData();
            SeedBuroEmployeeData();
        }

        private void SeedBuroLoanAccountData()
        {
            var loanAccounts = new List<BuroLoanAccount>
            {
                new()
                {
                    ItemId = "001"
                }
            };

            SetupRepositoryMock(loanAccounts);
        }

        private void SeedBuroLoanApplicationData()
        {
            var loanApplications = new List<BuroLoanApplication>
            {
                new()
                {
                    ItemId = _loanApplicationItemId,
                    ProductLineItemId = _loanProductLineItemId,
                    TenantId = _tenureId,
                    SelfNetIncome = new NetIncome
                    {
                        TotalIncome = 10000,
                        TotalExpense = 8000
                    },
                    FamilyNetIncome = new NetIncome
                    {
                        TotalIncome = 10000,
                        TotalExpense = 8000
                    },
                    FinalLoanAmount = 100000,
                    MemberItemId = _memberItemId
                }
            };

            SetupRepositoryMock(loanApplications);
        }

        private void SeedLoanProductLineData()
        {
            var loanProductLines = new List<LoanProductLine>
            {
                new()
                {
                    ItemId = _loanProductLineItemId,
                    AllowedTenures = new List<LoanLineTenure>
                    {
                        new()
                        {
                            TenureId = _tenureId,
                            NumberOfSchedules = 10,
                            Duration = new Period
                            {
                                Unit = EnumTenureUnit.Month,
                                Value = 18
                            }
                        }
                    }
                }
            };

            SetupRepositoryMock(loanProductLines);
        }

        private void SeedBuroEmployeeData()
        {
            var employees = new List<BuroEmployee>
            {
                new()
                {
                    ItemId = "001",
                    UserItemId = _userItemId
                }
            };

            SetupRepositoryMock(employees);
        }

        private SubmitLoanCommand MakeSubmitLoanCommand(EnumProductApplicationStatus status)
        {
            return new SubmitLoanCommand
            {
                LoanApplicationItemId = _loanApplicationItemId,
                Status = status,
                FinalLoanAmount = 10000,
                Remarks = "Test-001"
            };
        }

        [Fact]
        public async Task SubmitLoanAsync_SubmitNewLoanApplicationForReview_SuccessfulySubmitsTheApplication()
        {
            // Arrange
            var command = MakeSubmitLoanCommand(EnumProductApplicationStatus.InReview);

            // Act
            var response = await _service.SubmitLoanAsync(command);

            // Assert
            Assert.NotNull(response);
            Assert.True(response.Errors.IsValid);
            Assert.False(response.ErrorMessages.Any());
        }

        [Fact]
        public async Task SubmitLoanAsync_SubmitLoanApplicationCorrectionRequest_SuccessfulySubmitsTheApplication()
        {
            // Arrange
            var command = MakeSubmitLoanCommand(EnumProductApplicationStatus.Correction);

            // Act
            var response = await _service.SubmitLoanAsync(command);

            // Assert
            Assert.NotNull(response);
            Assert.True(response.Errors.IsValid);
            Assert.False(response.ErrorMessages.Any());
        }

        [Fact]
        public async Task SubmitLoanAsync_SubmitLoanApplicationForApproval_SuccessfulySubmitsTheApplication()
        {
            // Arrange
            var command = MakeSubmitLoanCommand(EnumProductApplicationStatus.ApprovalOngoing);

            // Act
            var response = await _service.SubmitLoanAsync(command);

            // Assert
            Assert.NotNull(response);
            Assert.True(response.Errors.IsValid);
            Assert.False(response.ErrorMessages.Any());
        }
    }
}
