using Domain.Commands.Register;
using Domain.Contracts.Member;
using Domain.Contracts.Register;
using FinMongoDbRepositories;
using Infrastructure.Constants;
using Infrastructure.Models;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Register;

public class CreateCommentRegisterCommandService : ICreateCommentRegisterCommandService
{
    private readonly ILogger<CreateCommentRegisterCommandService> _logger;
    private readonly IFinMongoDbRepository _repository;
    private readonly ISecurityContextProvider _securityContextProvider;
    private readonly ISharedService _sharedService;

    public CreateCommentRegisterCommandService(
        ILogger<CreateCommentRegisterCommandService> logger,
        ISecurityContextProvider securityContextProvider,
        IFinMongoDbRepository repository,
        ISharedService sharedService)
    {
        _logger = logger;
        _securityContextProvider = securityContextProvider;
        _repository = repository;
        _sharedService = sharedService;
    }
    
    public async Task<CommandResponse> CreateCommentRegisterAsync(CreateCommentRegisterCommand command)
    {
        _logger.LogInformation("Starting CreateCommentRegisterAsync for PostItemId: {PostItemId}", command.PostItemId);
        var commandResponse = new CommandResponse();

        try
        {
            await ProcessCommentRegisterAsync(command, commandResponse);

        }
        catch (Exception e)
        {
            _logger.LogError(e, "Error occurred during CreateCommentRegisterAsync for PostItemId: {PostItemId}", command.PostItemId);
            commandResponse.SetError(BuroErrorMessageKeys.ErrorOccurred, "An error occurred while creating comment.");
        }

        return commandResponse;
    }
    
    private async Task ProcessCommentRegisterAsync(CreateCommentRegisterCommand command, CommandResponse commandResponse)
    {
        var commentedItemId = Guid.NewGuid().ToString();
        var securityContext = _securityContextProvider.GetSecurityContext();
        var employeeInfo = await _sharedService.GetEmployeeInfoAsync(securityContext.UserId);
        var commentRegister = PrepareCommentRegisterEntity(command, employeeInfo, commentedItemId);
        await _repository.InsertOneAsync(commentRegister);
        _logger.LogInformation("Comment register created with comment itemId: {CommentedItemId}", commentedItemId);
    }
    
    private BuroPostComment PrepareCommentRegisterEntity(CreateCommentRegisterCommand command, EmployeeIdentityDto employeeInfo, string commentedItemId)
    {
        return new BuroPostComment
        {
            ItemId = commentedItemId,
            PostItemId = command.PostItemId,
            CommentText = command.CommentText,
            Tags = new[] { Tags.IsACommentRegister },
            CommentedEmployeeItemId = employeeInfo.EmployeeName,
            CommentedEmployeeName = employeeInfo.EmployeePin,
            CommentedEmployeePin = employeeInfo.EmployeeItemId,
            CommentedEmployeeDesignation = employeeInfo.DesignationTitle,
            CommentDate = DateTime.UtcNow
        };
    }
}