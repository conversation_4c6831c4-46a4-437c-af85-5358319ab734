using Domain.Contracts.Member;
using FinMongoDbRepositories;
using Infrastructure.Contracts;
using Infrastructure.Enums;
using Infrastructure.Extensions;
using Infrastructure.Models;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Entities.PrimaryEntities.BURO;

namespace Domain.Services
{
    public class SharedService : ISharedService
    {
        private readonly IFinMongoDbRepository _finMongoDbRepository;
        private readonly ILogger<SharedService> _logger;
        private readonly INotificationServiceClient _notificationServiceClient;
        private readonly ISequenceNumberClient _sequenceNumberClient;

        public SharedService(
            IFinMongoDbRepository finMongoDbRepository,
            ILogger<SharedService> logger,
            INotificationServiceClient notificationServiceClient,
            ISequenceNumberClient sequenceNumberClient)
        {
            _finMongoDbRepository = finMongoDbRepository;
            _logger = logger;
            _notificationServiceClient = notificationServiceClient;
            _sequenceNumberClient = sequenceNumberClient;
        }

        public async Task<BuroEmployee> GetEmployeeByDesignationForAnOfficeAsync(
            string officeItemId,
            string designationCode)
        {
            _logger.LogInformation("In GetEmployeesByDesignationCodeAndOfficeItemIdAsync with DesignationCode: {DesignationCode} and Office Id: {OfficeItemId}", designationCode, officeItemId);

            var employees = await _finMongoDbRepository.FindOneAsync<BuroEmployee>(
                e => e.CurrentOfficeItemId == officeItemId
                    && e.DesignationCode == designationCode);

            return employees;
        }

        public async Task NotifyUserAsync(
            string responseKey,
            object notificationPayload,
            string entityItemId,
            params string[] userId)
        {
            _logger.LogInformation("In NotifyUserAsync");

            try
            {
                var payloadWithResponse = MakeNotifierPayloadWithResponse(responseKey, notificationPayload, userId);

                await _notificationServiceClient.NotifyAsync(payloadWithResponse);

                _logger.LogInformation("Notification sent for Entity ID: {EntityItemId} to User ID: {UserId}", entityItemId, userId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send notification for Entity ID: {EntityItemId} to User ID: {UserId}", entityItemId, userId);
            }
        }

        private NotifierPayloadWithResponse MakeNotifierPayloadWithResponse(
            string ResponseKey,
            object notificationPayload,
            params string[] userId)
        {
            _logger.LogInformation("In MakeNotifierPayloadWithResponse");

            var response = new NotifierPayloadWithResponse
            {
                UserIds = userId.Select(i => Guid.Parse(i)).ToList(),
                NotificationType = userId.Length == 1
                    ? NotificationReceiverTypes.UserSpecificReceiverType
                    : NotificationReceiverTypes.BroadcastReceiverType,
                ResponseKey = ResponseKey,
                DenormalizedPayload = JsonConvert.SerializeObject(notificationPayload)
            };

            return response;
        }

        public async Task<string> GetSequenceNumberAsync(string context, string prefix = "")
        {
            var sequence = await _sequenceNumberClient.GetSequenceNumber(new SequenceNumberQuery { Context = context });

            if (sequence == null || sequence.CurrentNumber <= 0)
            {
                throw new InvalidOperationException("Invalid sequence number received from the sequence number service.");
            }

            var sequenceNumber = sequence.CurrentNumber.ToString().PadLeft(6, '0');

            return $"{prefix}{sequenceNumber}";
        }

        public string GenerateAbbreviation(string camelCasedStr)
        {
            if (camelCasedStr.IsNullOrEmptyOrWhiteSpace())
            {
                return string.Empty;
            }

            var capitalLatters = camelCasedStr.Where(char.IsUpper);

            return capitalLatters.Any() ? string.Concat(capitalLatters) : string.Empty;
        }

        public async Task<EmployeeIdentityDto> GetEmployeeInfoAsync(string userId)
        {
            var employeeInfo = await _finMongoDbRepository.FindWithProjectionAsync<BuroEmployee, EmployeeIdentityDto>(
                e => e.UserItemId == userId,
                e => new EmployeeIdentityDto
                {
                    EmployeeName = e.EmployeeName,
                    EmployeePin = e.EmployeePin,
                    DesignationTitle = e.DesignationTitle,
                    CurrentOfficeTitle = e.CurrentOfficeTitle,
                    CurrentOfficeCode = e.CurrentOfficeCode,
                    UserItemId = e.UserItemId,
                    EmployeeItemId = e.ItemId
                });

            return employeeInfo[0];
        }
    }
}
