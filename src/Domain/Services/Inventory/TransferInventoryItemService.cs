using Domain.Commands.Inventory;
using Domain.Contracts.Inventory;
using Domain.Contracts.Member;
using FinMongoDbRepositories;
using Infrastructure.Constants;
using Infrastructure.Enums;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Inventory;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Inventory;

public class TransferInventoryItemService
{
    private readonly IInventoryStockManagementService _inventoryStockManagementService;
    private readonly ISharedService _sharedService;
    private readonly IFinMongoDbRepository _repository;
    private readonly ISecurityContextProvider _securityContextProvider;

    public TransferInventoryItemService(IFinMongoDbRepository repository,
        ISecurityContextProvider securityContextProvider,
        IInventoryStockManagementService inventoryStockManagementService,
        ISharedService sharedService)
    {
        _repository = repository;
        _securityContextProvider = securityContextProvider;
        _inventoryStockManagementService = inventoryStockManagementService;
        _sharedService = sharedService;
    }

    public async Task TransferInventoryItemAsync(TransferRequestedInventoryListCommand command)
    {
        foreach (var transferInventoryItemCommand in command.TransferInventoryItems)
        {
            await TransferInventoryItemAsync(transferInventoryItemCommand, transferInventoryItemCommand.InventoryRequestItemId, true);
        }
    }

    public async Task TransferInventoryItemAsync(TransferInventoryItemCommand command, string? inventoryRequestItemId = null, bool requiresOnlyRecipientApproval = false)
    {
        var sourceEmployee = await GetLoggedInEmployeeAsync();
        var inventoryItem = await GetInventoryItemByIdAsync(command.InventoryItemId);
        var inventoryMovementPlan = PopulateInventoryMovementPlan(command, inventoryRequestItemId, inventoryItem, sourceEmployee);
        var (totalQuantity, totalPrice) = CalculateTotals(inventoryMovementPlan);

        var inventoryMovementOut = PopulateInventoryMovement(inventoryMovementPlan, sourceEmployee,
            totalQuantity, totalPrice, inventoryMovementPlan.SourceOfficeItemId, inventoryMovementPlan.SourceOfficeName,
            inventoryMovementPlan.RecipientOfficeItemId, inventoryMovementPlan.RecipientOfficeName, EnumInventoryMovementType.Out);

        var inventoryMovementIn = PopulateInventoryMovement(inventoryMovementPlan, sourceEmployee,
            totalQuantity, totalPrice, inventoryMovementPlan.RecipientOfficeItemId, inventoryMovementPlan.RecipientOfficeName,
            inventoryMovementPlan.SourceOfficeItemId, inventoryMovementPlan.SourceOfficeName, EnumInventoryMovementType.Out);

        await ProcessInventoryTransferAsync(inventoryMovementPlan, inventoryMovementOut, inventoryMovementIn,
            sourceEmployee, requiresOnlyRecipientApproval);
    }

    private async Task<BuroInventoryItem> GetInventoryItemByIdAsync(string inventoryItemId)
    {
        return await _repository.FindOneAsync<BuroInventoryItem>(item => item.ItemId == inventoryItemId);
    }


    private async Task ProcessInventoryTransferAsync(BuroInventoryMovementPlan inventoryMovementPlan,
        BuroInventoryMovement inventoryMovementOut,
        BuroInventoryMovement inventoryMovementIn,
        BuroEmployee sourceEmployee, bool requiresOnlyRecipientApproval)
    {
        await HandleApprovalAsync(inventoryMovementPlan, sourceEmployee, requiresOnlyRecipientApproval);
        await SaveInventoryPlanAndUpdateStocksAsync(inventoryMovementPlan, inventoryMovementOut, inventoryMovementIn);
    }

    private async Task HandleApprovalAsync(BuroInventoryMovementPlan inventoryMovementPlan,
        BuroEmployee sourceEmployee, bool requiresOnlyRecipientApproval)
    {
        if (IsBranchManager(sourceEmployee) ||
            (inventoryMovementPlan.InventoryItemDefinition == EnumInventoryItemDefinition.Request && requiresOnlyRecipientApproval))
        {
            await AssignRecipientApprovalAsync(inventoryMovementPlan, sourceEmployee);
        }
        else
        {
            await AssignSourceApprovalAsync(inventoryMovementPlan, sourceEmployee);
        }
    }

    private async Task AssignRecipientApprovalAsync(
        BuroInventoryMovementPlan inventoryMovementPlan,
        BuroEmployee sourceEmployee)
    {
        var approvalId = await AskApprovalForInventoryTransferAsync(
            inventoryMovementPlan,
            sourceEmployee,
            inventoryMovementPlan.RecipientOfficeItemId,
            Tags.IsATransferInventoryRecipientApproval,
            EnumApprovalCategoryType.InventoryTransferRecipientApproval);

        inventoryMovementPlan.TransferToApprovalItemId = approvalId;
        inventoryMovementPlan.ApprovalStatus = EnumInventoryApprovalStatus.PendingRecipientApproval;
    }

    private async Task AssignSourceApprovalAsync(
        BuroInventoryMovementPlan inventoryMovementPlan,
        BuroEmployee sourceEmployee)
    {
        var approvalId = await AskApprovalForInventoryTransferAsync(
            inventoryMovementPlan,
            sourceEmployee,
            inventoryMovementPlan.SourceOfficeItemId,
            Tags.IsATransferInventorySourceApproval,
            EnumApprovalCategoryType.InventoryTransferSourceApproval);

        inventoryMovementPlan.TransferFromApprovalItemId = approvalId;
        inventoryMovementPlan.ApprovalStatus = EnumInventoryApprovalStatus.PendingSourceApproval;
    }

    private async Task SaveInventoryPlanAndUpdateStocksAsync(
        BuroInventoryMovementPlan inventoryMovementPlan,
        BuroInventoryMovement inventoryMovementOut,
        BuroInventoryMovement inventoryMovementIn)
    {
        await _repository.InsertOneAsync(inventoryMovementPlan);

        await UpdateInventoryStockAsync(inventoryMovementOut, EnumStockUpdateType.AvailableOnly);
        await _inventoryStockManagementService.CreateOrUpdateInventoryStock(inventoryMovementIn,
            EnumStockUpdateType.AvailableOnly);

        await UpdateInventoryStockBreakdownAsync(inventoryMovementOut, inventoryMovementPlan,
            EnumStockUpdateType.AvailableOnly);
        await _inventoryStockManagementService.CreateOrUpdateInventoryStockBreakdownAsync(inventoryMovementPlan,
            inventoryMovementIn, EnumStockUpdateType.AvailableOnly);
    }

    private async Task UpdateInventoryStockAsync(BuroInventoryMovement inventoryMovement,
        EnumStockUpdateType updateType)
    {
        var currentStock = await _inventoryStockManagementService.GetInventoryStock(inventoryMovement.InventoryItemId,
            inventoryMovement.SourceOfficeItemId);

        await _inventoryStockManagementService.UpdateInventoryStock(inventoryMovement.MovementType,
            inventoryMovement.Quantity, inventoryMovement.UnitPrice, currentStock, inventoryMovement.CreatedBy,
            updateType);
    }

    private async Task UpdateInventoryStockBreakdownAsync(BuroInventoryMovement inventoryMovement,
        BuroInventoryMovementPlan movementPlan, EnumStockUpdateType updateType)
    {
        var stockBreakdownItemIds =
            movementPlan.InventoryItemDetails.Select(item => item.InventoryStockBreakdownItemId).ToList();
        var stockBreakdownList =
            await _inventoryStockManagementService.GetInventoryStockBreakdownList(stockBreakdownItemIds);

        var tasks = movementPlan.InventoryItemDetails
            .Select(inventoryDetail =>
            {
                var breakdown = stockBreakdownList.FirstOrDefault(item =>
                    item.ItemId == inventoryDetail.InventoryStockBreakdownItemId);
                if (breakdown == null) return null;
                return _inventoryStockManagementService.UpdateStocksInventoryStockBreakdown(
                    inventoryMovement.MovementType,
                    breakdown, inventoryDetail.Quantity, inventoryMovement.CreatedBy, updateType);
            })
            .Where(task => task != null)
            .ToList();

        await Task.WhenAll(tasks);
    }

    private bool IsBranchManager(BuroEmployee employee)
    {
        return employee.DesignationCode == BuroDesignationConstant.BranchManagerDesignationCode;
    }

    private async Task<BuroEmployee> GetLoggedInEmployeeAsync()
    {
        var userId = _securityContextProvider.GetSecurityContext().UserId;
        return await _repository.FindOneAsync<BuroEmployee>(item => item.UserItemId == userId);
    }

    private (int TotalQuantity, double TotalPrice) CalculateTotals(BuroInventoryMovementPlan command)
    {
        var totalQuantity = command.InventoryItemDetails.Sum(item => item.Quantity);
        var totalPrice = command.InventoryItemDetails.Sum(item => item.Price * item.Quantity);
        return (totalQuantity, totalPrice);
    }

    private BuroInventoryMovementPlan PopulateInventoryMovementPlan(TransferInventoryItemCommand command,
        string? inventoryRequestItemId,
        BuroInventoryItem inventoryItem,
        BuroEmployee employee)
    {
        return new BuroInventoryMovementPlan
        {
            ItemId = Guid.NewGuid().ToString(),
            InventoryItemId = inventoryItem.ItemId,
            InventoryName = inventoryItem.InventoryItemName,
            InventoryItemDefinition = inventoryItem.ItemDefinition,
            InventoryRequestItemId = inventoryItem.ItemDefinition == EnumInventoryItemDefinition.Request ? inventoryRequestItemId : null,
            CategoryItemId = inventoryItem.CategoryItemId,
            Category = inventoryItem.CategoryName,
            SubCategoryItemId = inventoryItem.SubCategoryItemId,
            SubCategory = inventoryItem.SubCategoryItemId,
            VendorItemId = command.VendorItemId,
            VendorName = command.VendorName,
            Reason = command.Reason,
            ApprovalStatus = EnumInventoryApprovalStatus.Pending,
            HasSerialNumber = inventoryItem.HasSerialNumber,
            RequiresExpiryDate = inventoryItem.RequiresExpiryDate,
            IsTransferred = true,
            SourceOfficeItemId = employee.CurrentOfficeItemId,
            SourceOfficeName = employee.CurrentOfficeTitle,
            RecipientOfficeItemId = command.TransferOfficeItemId,
            RecipientOfficeName = command.TransferOfficeName,
            PlanType = EnumInventoryMovementPlanType.Transfer,
            InventoryItemDetails = command.InventoryItemDetails.Select(detail => new InventoryItemDetail
            {
                InventoryStockBreakdownItemId = detail.InventoryStockBreakdownItemId,
                Quantity = detail.Quantity,
                SerialNumber = detail.SerialNumber,
                ExpiryDate = detail.ExpiryDate,
                Price = detail.Price
            }).ToList(),
            CreatedBy = employee.UserItemId,
            CreateDate = DateTime.UtcNow
        };
    }

    private BuroInventoryMovement PopulateInventoryMovement(BuroInventoryMovementPlan movementPlan,
        BuroEmployee employee,
        int totalQuantity,
        double totalPrice,
        string sourceOfficeItemId,
        string sourceOfficeName,
        string recipientOfficeItemId,
        string recipientOfficeName,
        EnumInventoryMovementType movementType)
    {
        var inventoryMovement = new BuroInventoryMovement
        {
            ItemId = Guid.NewGuid().ToString(),
            InventoryItemId = movementPlan.InventoryItemId,
            InventoryName = movementPlan.InventoryName,
            InventoryItemDefinition = movementPlan.InventoryItemDefinition,
            CategoryItemId = movementPlan.CategoryItemId,
            Category = movementPlan.Category,
            SubCategoryItemId = movementPlan.SubCategoryItemId,
            SubCategory = movementPlan.SubCategory,
            VendorItemId = movementPlan.VendorItemId,
            VendorName = movementPlan.VendorName,
            Quantity = totalQuantity,
            HasSerialNumber = movementPlan.HasSerialNumber,
            SerialNumber = string.Join(',', movementPlan.InventoryItemDetails.Select(item => item.SerialNumber)),
            RequiresExpiryDate = movementPlan.RequiresExpiryDate,
            ExpiryDate = movementPlan.InventoryItemDetails.Max(item => item.ExpiryDate),
            TotalPrice = totalPrice,
            MovementType = movementType,
            SourceOfficeItemId = sourceOfficeItemId,
            SourceOfficeName = sourceOfficeName,
            RecipientOfficeItemId = recipientOfficeItemId,
            RecipientOfficeName = recipientOfficeName,
            Reason = movementPlan.Reason,
            CreatedBy = employee.UserItemId,
            CreateDate = DateTime.UtcNow
        };
        return inventoryMovement;
    }

    private async Task<string> AskApprovalForInventoryTransferAsync(BuroInventoryMovementPlan inventoryMovementPlan,
        BuroEmployee sourceEmployee, string approverOfficeItemId, string tag, EnumApprovalCategoryType categoryType)
    {
        var approverEmployee = await _sharedService.GetEmployeeByDesignationForAnOfficeAsync(
            approverOfficeItemId,
            BuroDesignationConstant.BranchManagerDesignationCode);
        var approvalItemId =
            await CreateInventoryItemTransferApprovalAsync(inventoryMovementPlan,
                sourceEmployee,
                approverEmployee,
                tag,
                categoryType);

        var notificationPayload = MakeNotificationPayloadForApprover(inventoryMovementPlan,
            sourceEmployee.EmployeeName, approvalItemId, approverEmployee);

        await _sharedService.NotifyUserAsync(BuroNotificationKeys.InventoryTransferItemBranchManagerInvited,
            notificationPayload, inventoryMovementPlan.ItemId, approverEmployee.UserItemId);
        return approvalItemId;
    }

    private async Task<string> CreateInventoryItemTransferApprovalAsync(BuroInventoryMovementPlan inventoryMovementPlan,
        BuroEmployee sourceEmployee, BuroEmployee approverEmployee, string tag, EnumApprovalCategoryType categoryType)
    {
        var approval = new BuroApproval
        {
            ItemId = Guid.NewGuid().ToString(),
            Tags = new[] { tag },
            CreatedBy = sourceEmployee.UserItemId,
            LastUpdatedBy = sourceEmployee.UserItemId,
            RelatedEntityItemId = inventoryMovementPlan.ItemId,
            RelatedEntityName = nameof(BuroInventoryMovementPlan),
            RequestedFromPersonItemId = sourceEmployee.PersonItemId,
            RequestedFromEmployeeItemId = sourceEmployee.ItemId,
            RequestedFromEmployeePIN = sourceEmployee.EmployeePin,
            RequestedFromEmployeeName = sourceEmployee.EmployeeName,
            ActionedByPersonItemId = approverEmployee.PersonItemId,
            ActionedByEmployeeItemId = approverEmployee.ItemId,
            ActionedByEmployeePIN = approverEmployee.EmployeePin,
            ActionedByEmployeeName = approverEmployee.EmployeeName,
            ActionedByEmployeeDesignation = approverEmployee.DesignationTitle,
            Status = EnumApprovalStatusType.Pending,
            Category = categoryType
        };
        await _repository.InsertOneAsync(approval);

        return approval.ItemId;
    }

    private static object MakeNotificationPayloadForApprover(
        BuroInventoryMovementPlan inventoryMovementPlan,
        string addedBy,
        string approvalItemId,
        BuroEmployee manager)
    {
        return new
        {
            InventoryMovementPlanItemId = inventoryMovementPlan.ItemId,
            AssignedAt = DateTime.UtcNow,
            AssignedBy = addedBy,
            ApprovalItemId = approvalItemId,
            ApproverName = manager.EmployeeName,
            ApproverPin = manager.EmployeePin,
            ApproverDesignation = manager.DesignationTitle
        };
    }
}