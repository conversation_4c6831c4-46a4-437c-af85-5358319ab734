using Domain.Commands.Inventory;
using Domain.Contracts.Inventory;
using Domain.Contracts.Member;
using FinMongoDbRepositories;
using Infrastructure.Constants;
using Infrastructure.Enums;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Inventory;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Inventory;

public class OutInventoryItemService
{
    private readonly IInventoryStockManagementService _inventoryStockManagementService;
    private readonly ILogger<OutInventoryItemService> _logger;
    private readonly IFinMongoDbRepository _repository;
    private readonly ISecurityContextProvider _securityContextProvider;
    private readonly ISharedService _sharedService;

    public OutInventoryItemService(
        ILogger<OutInventoryItemService> logger,
        IFinMongoDbRepository repository,
        ISecurityContextProvider securityContextProvider,
        IInventoryStockManagementService inventoryStockManagementService,
        ISharedService sharedService)
    {
        _logger = logger;
        _repository = repository;
        _securityContextProvider = securityContextProvider;
        _inventoryStockManagementService = inventoryStockManagementService;
        _sharedService = sharedService;
    }

    public async Task OutInventoryItemAsync(OutInventoryItemCommand command)
    {
        _logger.LogInformation("OutInventoryItemAsync started..");
        var loggedInEmployee = await GetLoggedInEmployeeAsync();
        var inventoryItem = await GetInventoryItemByIdAsync(command.InventoryItemId);
        var (totalQuantity, totalPrice) = CalculateTotals(command);

        var inventoryMovementPlan = PopulateInventoryMovementPlan(command, inventoryItem, loggedInEmployee);
        var inventoryMovement = PopulateInventoryMovement(command, inventoryItem, loggedInEmployee, totalQuantity, totalPrice);

        await ProcessInventoryOut(inventoryMovementPlan, inventoryMovement, command, loggedInEmployee);
        _logger.LogInformation("OutInventoryItemAsync finished..");
    }

    private async Task<BuroInventoryItem> GetInventoryItemByIdAsync(string inventoryItemId)
    {
        return await _repository.FindOneAsync<BuroInventoryItem>(item => item.ItemId == inventoryItemId);
    }

    private (int TotalQuantity, double TotalPrice) CalculateTotals(OutInventoryItemCommand command)
    {
        var totalQuantity = command.InventoryItemDetails.Sum(item => item.Quantity);
        var totalPrice = command.InventoryItemDetails.Sum(item => item.Price * item.Quantity);
        return (totalQuantity, totalPrice);
    }

    private async Task ProcessInventoryOut(
        BuroInventoryMovementPlan inventoryMovementPlan,
        BuroInventoryMovement inventoryMovement,
        OutInventoryItemCommand command,
        BuroEmployee loggedInEmployee)
    {
        if (IsBranchManager(loggedInEmployee))
            await ProcessBranchManagerInventoryOut(inventoryMovementPlan, inventoryMovement, command);
        else
            await ProcessRegularEmployeeInventoryOut(inventoryMovementPlan, inventoryMovement, command,
                loggedInEmployee);
    }

    private bool IsBranchManager(BuroEmployee employee)
    {
        return employee.DesignationCode == BuroDesignationConstant.BranchManagerDesignationCode;
    }


    private async Task ProcessBranchManagerInventoryOut(
        BuroInventoryMovementPlan plan,
        BuroInventoryMovement movement,
        OutInventoryItemCommand command)
    {
        plan.ApprovalStatus = EnumInventoryApprovalStatus.Approved;
        await _repository.InsertOneAsync(movement);
        await _repository.InsertOneAsync(plan);
        await UpdateInventoryStockAsync(movement, EnumStockUpdateType.BothActualAndAvailable);
        await UpdateInventoryStockBreakdownAsync(movement, command, EnumStockUpdateType.BothActualAndAvailable);
    }

    private async Task ProcessRegularEmployeeInventoryOut(
        BuroInventoryMovementPlan plan,
        BuroInventoryMovement movement,
        OutInventoryItemCommand command,
        BuroEmployee employee)
    {
        var approvalItemId = await AskApprovalForInventoryOutAsync(plan, employee);
        plan.OutApprovalItemId = approvalItemId;
        await _repository.InsertOneAsync(plan);

        await UpdateInventoryStockAsync(movement, EnumStockUpdateType.AvailableOnly);
        await UpdateInventoryStockBreakdownAsync(movement, command, EnumStockUpdateType.AvailableOnly);
    }

    private async Task<string> AskApprovalForInventoryOutAsync(BuroInventoryMovementPlan inventoryMovementPlan,
        BuroEmployee loggedInEmployee)
    {
        var approverEmployee = await _sharedService.GetEmployeeByDesignationForAnOfficeAsync(
            loggedInEmployee.CurrentOfficeItemId,
            BuroDesignationConstant.BranchManagerDesignationCode);
        var approvalItemId =
            await CreateInventoryOutItemApprovalAsync(inventoryMovementPlan, loggedInEmployee, approverEmployee);

        var notificationPayload = MakeNotificationPayloadForBranchManger(inventoryMovementPlan,
            loggedInEmployee.EmployeeName, approvalItemId, approverEmployee);

        await _sharedService.NotifyUserAsync(BuroNotificationKeys.InventoryOutItemBranchManagerInvited,
            notificationPayload, inventoryMovementPlan.ItemId, approverEmployee.UserItemId);
        return approvalItemId;
    }

    private static object MakeNotificationPayloadForBranchManger(
        BuroInventoryMovementPlan inventoryMovementPlan,
        string addedBy,
        string approvalItemId,
        BuroEmployee manager)
    {
        return new
        {
            InventoryMovementPlanItemId = inventoryMovementPlan.ItemId,
            AssignedAt = DateTime.UtcNow,
            AssignedBy = addedBy,
            ApprovalItemId = approvalItemId,
            ApproverName = manager.EmployeeName,
            ApproverPin = manager.EmployeePin,
            ApproverDesignation = manager.DesignationTitle
        };
    }

    private async Task<string> CreateInventoryOutItemApprovalAsync(BuroInventoryMovementPlan inventoryMovementPlan,
        BuroEmployee loggedInEmployee, BuroEmployee approverEmployee)
    {
        var approval = new BuroApproval
        {
            ItemId = Guid.NewGuid().ToString(),
            Tags = new[] { Tags.IsAOutInventoryApproval },
            CreatedBy = loggedInEmployee.UserItemId,
            LastUpdatedBy = loggedInEmployee.UserItemId,
            RelatedEntityItemId = inventoryMovementPlan.ItemId,
            RelatedEntityName = nameof(BuroInventoryMovementPlan),
            RequestedFromPersonItemId = loggedInEmployee.PersonItemId,
            RequestedFromEmployeeItemId = loggedInEmployee.ItemId,
            RequestedFromEmployeePIN = loggedInEmployee.EmployeePin,
            RequestedFromEmployeeName = loggedInEmployee.EmployeeName,
            ActionedByPersonItemId = approverEmployee.PersonItemId,
            ActionedByEmployeeItemId = approverEmployee.ItemId,
            ActionedByEmployeePIN = approverEmployee.EmployeePin,
            ActionedByEmployeeName = approverEmployee.EmployeeName,
            ActionedByEmployeeDesignation = approverEmployee.DesignationTitle,
            Status = EnumApprovalStatusType.Pending,
            Category = EnumApprovalCategoryType.InventoryOutApproval
        };
        await _repository.InsertOneAsync(approval);

        return approval.ItemId;
    }

    private BuroInventoryMovementPlan PopulateInventoryMovementPlan(OutInventoryItemCommand command,
        BuroInventoryItem inventoryItem,
        BuroEmployee employee)
    {
        return new BuroInventoryMovementPlan
        {
            ItemId = Guid.NewGuid().ToString(),
            InventoryItemId = inventoryItem.ItemId,
            InventoryName = inventoryItem.InventoryItemName,
            InventoryItemDefinition = inventoryItem.ItemDefinition,
            CategoryItemId = inventoryItem.CategoryItemId,
            Category = inventoryItem.CategoryName,
            SubCategoryItemId = inventoryItem.SubCategoryItemId,
            SubCategory = inventoryItem.SubCategoryName,
            VendorItemId = command.VendorItemId,
            VendorName = command.VendorName,
            Reason = command.Reason,
            ApprovalStatus = EnumInventoryApprovalStatus.Pending,
            HasSerialNumber = inventoryItem.HasSerialNumber,
            RequiresExpiryDate = inventoryItem.RequiresExpiryDate,
            RecipientOfficeItemId = command.OutDestination == EnumInventoryOutDestination.Office
                ? employee.CurrentOfficeItemId
                : null,
            RecipientOfficeName = command.OutDestination == EnumInventoryOutDestination.Office
                ? employee.CurrentOfficeTitle
                : null,
            RecipientEmployeeItemId = command.TransferEmployeeItemId,
            RecipientEmployeeName = command.TransferEmployeeName,
            PlanType = EnumInventoryMovementPlanType.Out,
            InventoryItemDetails = command.InventoryItemDetails.Select(detail => new InventoryItemDetail
            {
                InventoryStockBreakdownItemId = detail.InventoryStockBreakdownItemId,
                Quantity = detail.Quantity,
                SerialNumber = detail.SerialNumber,
                ExpiryDate = detail.ExpiryDate,
                Price = detail.Price
            }).ToList(),
            CreatedBy = employee.UserItemId,
            CreateDate = DateTime.UtcNow
        };
    }

    private async Task UpdateInventoryStockBreakdownAsync(BuroInventoryMovement inventoryMovement,
        OutInventoryItemCommand command, EnumStockUpdateType updateType)
    {
        var stockBreakdownItemIds =
            command.InventoryItemDetails.Select(item => item.InventoryStockBreakdownItemId).ToList();
        var stockBreakdownList =
            await _inventoryStockManagementService.GetInventoryStockBreakdownList(stockBreakdownItemIds);

        _logger.LogInformation("UpdateInventoryStockBreakdownAsync :: Stock Breakdown List :: {StockBreakdownList}", JsonConvert.SerializeObject(stockBreakdownList));

        var tasks = command.InventoryItemDetails
            .Select(inventoryDetail =>
            {
                var breakdown = stockBreakdownList.FirstOrDefault(item =>
                    item.ItemId == inventoryDetail.InventoryStockBreakdownItemId);
                if (breakdown == null) return null;
                return _inventoryStockManagementService.UpdateStocksInventoryStockBreakdown(
                    inventoryMovement.MovementType,
                    breakdown, inventoryDetail.Quantity, inventoryMovement.CreatedBy, updateType);
            })
            .Where(task => task != null)
            .ToList();

        await Task.WhenAll(tasks);
    }

    private async Task UpdateInventoryStockAsync(BuroInventoryMovement inventoryMovement,
        EnumStockUpdateType updateType)
    {
        var currentStock = await _inventoryStockManagementService.GetInventoryStock(inventoryMovement.InventoryItemId,
            inventoryMovement.SourceOfficeItemId);

        _logger.LogInformation("UpdateInventoryStockAsync :: Current Stock :: {Stock}", JsonConvert.SerializeObject(currentStock));

        await _inventoryStockManagementService.UpdateInventoryStock(inventoryMovement.MovementType,
            inventoryMovement.Quantity, inventoryMovement.UnitPrice, currentStock, inventoryMovement.CreatedBy,
            updateType);
    }

    private BuroInventoryMovement PopulateInventoryMovement(OutInventoryItemCommand command,
        BuroInventoryItem inventoryItem, BuroEmployee employee,
        int totalQuantity, double totalPrice)
    {
        var inventoryMovement = new BuroInventoryMovement
        {
            ItemId = Guid.NewGuid().ToString(),
            SourceOfficeItemId = employee.CurrentOfficeItemId,
            SourceOfficeName = employee.CurrentOfficeTitle,
            InventoryItemId = inventoryItem.ItemId,
            InventoryName = inventoryItem.InventoryItemName,
            CategoryItemId = inventoryItem.CategoryItemId,
            Category = inventoryItem.CategoryName,
            SubCategoryItemId = inventoryItem.SubCategoryItemId,
            SubCategory = inventoryItem.SubCategoryName,
            VendorItemId = command.VendorItemId,
            VendorName = command.VendorName,
            Quantity = totalQuantity,
            HasSerialNumber = inventoryItem.HasSerialNumber,
            SerialNumber = string.Join(',', command.InventoryItemDetails.Select(item => item.SerialNumber)),
            RequiresExpiryDate = inventoryItem.RequiresExpiryDate,
            ExpiryDate = command.InventoryItemDetails.Max(item => item.ExpiryDate),
            TotalPrice = totalPrice,
            MovementType = EnumInventoryMovementType.Out,
            OutDestination = command.OutDestination,
            RecipientEmployeeItemId = command.TransferEmployeeItemId,
            RecipientEmployeeName = command.TransferEmployeeName,
            RecipientOfficeItemId = command.OutDestination == EnumInventoryOutDestination.Office
                ? employee.CurrentOfficeItemId
                : null,
            RecipientOfficeName = command.OutDestination == EnumInventoryOutDestination.Office
                ? employee.CurrentOfficeTitle
                : null,
            Reason = command.Reason,
            CreatedBy = employee.UserItemId,
            CreateDate = DateTime.UtcNow
        };
        return inventoryMovement;
    }

    private async Task<BuroEmployee> GetLoggedInEmployeeAsync()
    {
        var userId = _securityContextProvider.GetSecurityContext().UserId;
        return await _repository.FindOneAsync<BuroEmployee>(item => item.UserItemId == userId);
    }
}