using Domain.Commands.Account;
using Domain.Contracts.Account;
using Infrastructure.Constants;
using Infrastructure.Events.Account;
using Infrastructure.Extensions;
using Microsoft.Extensions.Logging;
using MongoDB.Bson;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Product;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Account
{
    public class SubmitLoanCommandService : ISubmitLoanCommandService
    {
        private readonly ILogger<SubmitLoanCommandService> _logger;
        private readonly IRepository _repository;
        private readonly ISecurityContextProvider _securityContextProvider;
        private readonly IServiceClient _serviceClient;

        public SubmitLoanCommandService(
            ILogger<SubmitLoanCommandService> logger,
            IRepository repository,
            ISecurityContextProvider securityContextProvider,
            IServiceClient serviceClient)
        {
            _logger = logger;
            _repository = repository;
            _securityContextProvider = securityContextProvider;
            _serviceClient = serviceClient;
        }

        public async Task<CommandResponse> SubmitLoanAsync(SubmitLoanCommand command)
        {
            _logger.LogInformation("Starting submitting loan Application with ID: {LoanApplicationItemId}", command.LoanApplicationItemId);

            var commandResponse = new CommandResponse();

            try
            {
                var currentEmployee = await GetCurrentEmployeeAsync();
                var cashFlowAnalysis = command.Status == EnumProductApplicationStatus.InReview
                    ? await AnalyzeLoanApplication(command)
                    : null;

                await UpdateLoanApplicationAsync(command, currentEmployee.UserItemId, cashFlowAnalysis);
                await SaveLoanApplicationHistoryAsync(command, currentEmployee);

                SendToHandleLoanApplication(command);

                _logger.LogInformation("Successfully submitted loan with ID: {LoanApplicationItemId}", command.LoanApplicationItemId);
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Error occurred while submitting loan application with ID: {LoanApplicationItemId}", command.LoanApplicationItemId);

                commandResponse.SetError(BuroErrorMessageKeys.ErrorOccurred, "An error occurred while submitting loan application");
            }

            return commandResponse;
        }

        private void SendToHandleLoanApplication(SubmitLoanCommand command)
        {
            var payload = new LoanApplicationPostProcessingEvent
            {
                LoanApplicationItemId = command.LoanApplicationItemId,
                Status = command.Status,
                Remarks = command.Remarks
            };

            _serviceClient.SendToQueue<bool>(QueueNames.BuroProductAccountQueue, payload);

            _logger.LogInformation("Loan application post processing initiated for Application ID: {LoanApplicationItemId}", command.LoanApplicationItemId);
        }

        private async Task<BuroEmployee> GetCurrentEmployeeAsync()
        {
            var securityContext = _securityContextProvider.GetSecurityContext();
            var currentEmployee = await _repository.GetItemAsync<BuroEmployee>(e => e.UserItemId == securityContext.UserId);

            return currentEmployee;
        }

        private async Task SaveLoanApplicationHistoryAsync(SubmitLoanCommand command, BuroEmployee employee)
        {
            _logger.LogInformation("Saving Loan application history for loan ID: {LoanApplicationItemId}", command.LoanApplicationItemId);

            var history = new BuroProductApplicationHistory
            {
                ApplicationItemId = command.LoanApplicationItemId,
                ActionByEmployeeItemId = employee.ItemId,
                ActionByEmployeePin = employee.EmployeePin,
                ActionByEmployeeName = employee.EmployeeName,
                ActionByEmployeeDesginationItemId = employee.DesignationItemId,
                ActionByEmployeeDesginationTitle = employee.DesignationTitle,
                ActionByEmployeeOfficeItemId = employee.CurrentOfficeItemId,
                ActionByEmployeeOfficeName = employee.CurrentOfficeTitle,
                ActionByEmployeeOfficeCode = employee.CurrentOfficeCode,
                Status = command.Status,
                ProductType = EnumProductType.Loan
            };

            history.AddEntityBasicInfo();

            await _repository.SaveAsync(history);
        }

        private async Task UpdateLoanApplicationAsync(SubmitLoanCommand command, string userItemId, CashFlowAnalysis? cashFlowAnalysis)
        {
            var properties = new Dictionary<string, object>
            {
                {nameof(BuroLoanApplication.LastUpdateDate), DateTime.UtcNow },
                {nameof(BuroLoanApplication.LastUpdatedBy), userItemId },
                {nameof(BuroLoanApplication.Status), command.Status }
            };

            if (command.FinalLoanAmount.HasValue)
            {
                properties.Add(nameof(BuroLoanApplication.FinalLoanAmount), command.FinalLoanAmount.Value);
            }

            if (command.Status == EnumProductApplicationStatus.InReview && cashFlowAnalysis != null)
            {
                properties.Add(nameof(BuroLoanApplication.CashFlowAnalysisDetails), cashFlowAnalysis.ToBsonDocument());
            }

            if (!string.IsNullOrWhiteSpace(command.Remarks))
            {
                if (command.Status == EnumProductApplicationStatus.Correction)
                {
                    properties.Add(nameof(BuroLoanApplication.CorrectionRemarks), command.Remarks);
                }
                else
                {
                    properties.Add(nameof(BuroLoanApplication.Remarks), command.Remarks);
                }
            }

            await _repository.UpdateAsync<BuroLoanApplication>(l => l.ItemId == command.LoanApplicationItemId, properties);
        }

        private async Task<CashFlowAnalysis> AnalyzeLoanApplication(SubmitLoanCommand command)
        {
            var loanApplication = await GetLoanApplicationByItemId(command.LoanApplicationItemId);
            var productLine = await GetLoanProductLineAsync(loanApplication.ProductLineItemId);
            var loandTenure = loanApplication.TenureDetails;

            var businessNetIncome = (loanApplication.SelfNetIncome.TotalIncome - loanApplication.SelfNetIncome.TotalExpense);
            var familyNetIncome = (loanApplication.FamilyNetIncome.TotalIncome - loanApplication.FamilyNetIncome.TotalExpense);
            var totalNetIncome = businessNetIncome + familyNetIncome;

            var cashFlowAdjustmentFactor = await GetCashFlowAdjustmentFactor(loanApplication.MemberItemId);
            var loanPaymentAbility = totalNetIncome * cashFlowAdjustmentFactor;
            var interestRate = productLine.InterestRate;
            var numberOfInstalment = loandTenure?.NumberOfSchedules ?? 0;
            var loanTenureDuration = CalculateLoanTenureDuration(loandTenure); // NOSONAR WIP we may have to change this latter

            var proposedLoanAmount = (loanPaymentAbility + numberOfInstalment) / Math.Pow((1 + (interestRate / 100)), loanTenureDuration);
            var isPositive = proposedLoanAmount >= (loanApplication.FinalLoanAmount ?? 0);

            var cashFlowDetails = new CashFlowAnalysis
            {
                IsPositive = isPositive,
                ProposedLoanAmount = proposedLoanAmount,
                LoanPaymentAbility = loanPaymentAbility,
                CashFlowAdjustmentFactor = cashFlowAdjustmentFactor,
                TotalNetIncome = totalNetIncome,
                InterestRate = interestRate,
                NumberOfInstalment = numberOfInstalment,
                LoanTenureDuration = loanTenureDuration
            };

            return cashFlowDetails;
        }

        private static int CalculateLoanTenureDuration(LoanLineTenure? loandTenure)
        {
            if (loandTenure == null)
            {
                return 0;
            }

            return loandTenure.Duration.Unit switch
            {
                EnumTenureUnit.Month => loandTenure.Duration.Value,
                EnumTenureUnit.Year => loandTenure.Duration.Value * 12,
                _ => default
            };
        }

        private async Task<double> GetCashFlowAdjustmentFactor(string memberItemId)
        {
            var hasExistingLoan = await _repository.ExistsAsync<BuroLoanAccount>(l => l.MemberItemId == memberItemId);

            return hasExistingLoan ? 50 : 40; // NOSONAR WIP Need to move this inside loan configuration
        }

        private async Task<BuroLoanApplication> GetLoanApplicationByItemId(string loanApplicationItemId)
        {
            return await _repository.GetItemAsync<BuroLoanApplication>(l => l.ItemId == loanApplicationItemId)
                ?? throw new InvalidOperationException(string.Format(BuroErrorMessageKeys.RequiredDataNotFound, "LOANAPPLICATION"));
        }

        private async Task<LoanProductLine> GetLoanProductLineAsync(string productLineItemId)
        {
            return await _repository.GetItemAsync<LoanProductLine>(lp => lp.ItemId == productLineItemId)
                ?? throw new InvalidOperationException(string.Format(BuroErrorMessageKeys.RequiredDataNotFound, "LOANPRODUCTLINE"));
        }
    }
}
