using Domain.Commands.ConsentForm;
using Domain.Contracts.ConsentForm;
using Domain.Contracts.Member;
using FinMongoDbRepositories;
using Infrastructure.Constants;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.ConsentForm;

public class CreateConsentFormService : ICreateConsentFormCommandService
{
    private readonly ILogger<CreateConsentFormService> _logger;
    private readonly IFinMongoDbRepository _repository;
    private readonly ISecurityContextProvider _securityContextProvider;
    private readonly ISharedService _sharedService;

    public CreateConsentFormService(
        ILogger<CreateConsentFormService> logger,
        ISecurityContextProvider securityContextProvider,
        IFinMongoDbRepository repository,
        ISharedService sharedService)
    {
        _logger = logger;
        _securityContextProvider = securityContextProvider;
        _repository = repository;
        _sharedService = sharedService;
    }


    public async Task<CommandResponse> CreateConsentFormAsync(CreateConsentFormCommand command)
    {
        _logger.LogInformation("Starting consent form creation for member itemId: {MemberItemId}",
            command.MemberItemId);

        var commandResponse = new CommandResponse();

        try
        {
            var existingConsent = await _repository.FindOneAsync<BuroConsentForm>(c =>
                c.MemberItemId == command.MemberItemId &&
                c.ReceiveAccountItemId == command.ReceiveAccountItemId);

            if (existingConsent != null) throw new ArgumentException("Consent Form already exists");


            var fundAccount = await _repository.FindWithProjectionAsync<BuroGeneralSavingsAccount, string>(
                                  g => g.ItemId == command.FundAccountItemId && g.MemberItemId == command.MemberItemId,
                                  g => g.AccountSequenceNumber) ??
                              throw new ArgumentException(
                                  $"Fund account not found for ItemId: {command.FundAccountItemId}");

            var receiveAccountSequence = command.ReceiveAccountCode.ToUpper() switch
            {
                "CS" => await _repository.FindWithProjectionAsync<BuroContractualSavingsAccount, string>(
                    c => c.ItemId == command.ReceiveAccountItemId && c.MemberItemId == command.MemberItemId,
                    c => c.AccountSequenceNumber),
                "LOAN" => await _repository.FindWithProjectionAsync<BuroLoanAccount, string>(
                    l => l.ItemId == command.ReceiveAccountItemId && l.MemberItemId == command.MemberItemId,
                    l => l.AccountSequenceNumber),
                _ => throw new ArgumentException($"Invalid receive account code: {command.ReceiveAccountCode}")
            };

            var receiveAccount = receiveAccountSequence.FirstOrDefault()
                                 ?? throw new ArgumentException(
                                     $"{command.ReceiveAccountCode} account not found for ItemId: {command.ReceiveAccountItemId}");

            await SubmitConsentForm(command, fundAccount[0], receiveAccount);

            _logger.LogInformation("CreateConsentFormAsync process completed successfully");
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Error creating consent form for member itemId: {MemberItemId}", command.MemberItemId);
            commandResponse.SetError(BuroErrorMessageKeys.ErrorOccurred, e.Message);
        }

        return commandResponse;
    }

    private async Task SubmitConsentForm(CreateConsentFormCommand command, string fundAccount, string receiveAccount)
    {
        var currentEmployee = await GetLoggedInEmployeeDataAsync();
        var approverEmployee = await _sharedService.GetEmployeeByDesignationForAnOfficeAsync(
            currentEmployee.CurrentOfficeItemId, BuroDesignationConstant.BranchManagerDesignationCode);
        var approvalItemId =
            await SubmitConsentFormApprovalAsync(command, fundAccount, receiveAccount, currentEmployee,
                approverEmployee);
        await CreateBuroConsentFromAsync(approvalItemId, command, fundAccount, receiveAccount);
    }

    private async Task<BuroEmployee> GetLoggedInEmployeeDataAsync()
    {
        var context = _securityContextProvider.GetSecurityContext();
        var employee = await _repository.FindOneAsync<BuroEmployee>(e => e.UserItemId == context.UserId);

        return employee;
    }

    private async Task<string> SubmitConsentFormApprovalAsync(
        CreateConsentFormCommand command,
        string fundAccount,
        string receiveAccount,
        BuroEmployee initiator,
        BuroEmployee manager)
    {
        _logger.LogInformation("Starting SubmitConsentFormApprovalAsync");

        var securityContext = _securityContextProvider.GetSecurityContext();
        var approvalEntityItemId =
            await CreateConsentFormApprovalAsync(command, fundAccount, receiveAccount, initiator, manager);
        var notificationPayload = MakeNotificationPayloadForBranchManager(command, command.ConsentFormItemId,
            securityContext.DisplayName, approvalEntityItemId, manager);

        await _sharedService.NotifyUserAsync(
            BuroNotificationKeys.ConsentFormBranchManagerApprovalCreated,
            notificationPayload,
            command.ConsentFormItemId,
            manager.UserItemId);

        _logger.LogInformation("Ending SubmitConsentFormApprovalAsync with approval itemId {approvalEntityItemId}",
            approvalEntityItemId);
        return approvalEntityItemId;
    }

    private async Task<string> CreateConsentFormApprovalAsync(CreateConsentFormCommand command, string fundAccount,
        string receiveAccount, BuroEmployee approvalInitiator, BuroEmployee approver)
    {
        _logger.LogInformation("Starting CreateConsentFormApprovalAsync");

        var currentUserId = approvalInitiator.UserItemId;

        var approval = new BuroApproval
        {
            ItemId = Guid.NewGuid().ToString(),
            Tags = new[] { Tags.IsAConsentFormApproval },
            CreatedBy = currentUserId,
            LastUpdatedBy = currentUserId,
            RelatedEntityItemId = command.ConsentFormItemId,
            RelatedEntityName = nameof(BuroConsentForm),
            RequestedFromPersonItemId = approvalInitiator.PersonItemId,
            RequestedFromEmployeeItemId = approvalInitiator.ItemId,
            RequestedFromEmployeePIN = approvalInitiator.EmployeePin,
            RequestedFromEmployeeName = approvalInitiator.EmployeeName,
            ActionedByPersonItemId = approver.PersonItemId,
            ActionedByEmployeeItemId = approver.ItemId,
            ActionedByEmployeePIN = approver.EmployeePin,
            ActionedByEmployeeName = approver.EmployeeName,
            ActionedByEmployeeDesignation = approver.DesignationTitle,
            Status = EnumApprovalStatusType.Pending,
            Category = EnumApprovalCategoryType.ConsentFormApproval,
            MetaData = new List<MetaData>
            {
                new()
                {
                    Key = nameof(command.FundAccountCode), Value = JsonConvert.SerializeObject(command.FundAccountCode)
                },
                new() { Key = "FundAccountNumber", Value = JsonConvert.SerializeObject(fundAccount) },
                new()
                {
                    Key = nameof(command.ReceiveAccountCode),
                    Value = JsonConvert.SerializeObject(command.ReceiveAccountCode)
                },
                new() { Key = "ReceiveAccountNumber", Value = JsonConvert.SerializeObject(receiveAccount) }
            },
            IdsAllowedToRead = new[] { currentUserId },
            IdsAllowedToUpdate = new[] { currentUserId },
            IdsAllowedToDelete = new[] { currentUserId },
            IdsAllowedToWrite = new[] { currentUserId }
        };


        await _repository.InsertOneAsync(approval);

        _logger.LogInformation("Consent form approval data added with approval Id: {ItemId}", approval.ItemId);

        return approval.ItemId;
    }

    private static object MakeNotificationPayloadForBranchManager(
        CreateConsentFormCommand command,
        string entityItemId,
        string addedBy,
        string approvalItemId,
        BuroEmployee manager)
    {
        return new
        {
            command.MemberItemId,
            RequestItemId = entityItemId,
            AssignedAt = DateTime.UtcNow,
            AssignedBy = addedBy,
            ApprovalItemId = approvalItemId,
            ApproverName = manager.EmployeeName,
            ApproverPin = manager.EmployeePin,
            ApproverDesignation = manager.DesignationTitle,
            CenterName = manager.CurrentCenterTitle
        };
    }

    private async Task CreateBuroConsentFromAsync(string approvalItemId, CreateConsentFormCommand command,
        string fundAccount,
        string receiveAccount)
    {
        _logger.LogInformation("Starting CreateBuroConsentFromAsync");
        var consentFrom = new BuroConsentForm
        {
            ItemId = command.ConsentFormItemId,
            FundAccountCode = command.FundAccountCode,
            FundAccountItemId = command.FundAccountItemId,
            FundAccountSequenceNumber = fundAccount,
            ReceiveAccountCode = command.ReceiveAccountCode,
            ReceiveAccountItemId = command.ReceiveAccountItemId,
            ReceiveAccountSequenceNumber = receiveAccount,
            MemberItemId = command.MemberItemId,
            ApprovalItemId = approvalItemId,
            Status = EnumConsentFormApprovalStatus.Submitted,
            ConsentFormDocumentId = command.ConsentFormDocumentItemId,
            Tags = new[] { Tags.IsAConsentForm }
        };
        await _repository.InsertOneAsync(consentFrom);
        _logger.LogInformation("Ending CreateBuroConsentFromAsync");
    }
}