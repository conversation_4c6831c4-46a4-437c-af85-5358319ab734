using Domain.Commands.Voucher;
using Domain.Contracts.Voucher;
using Infrastructure.Constants;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Voucher;

public class DeleteManualVoucherService : IDeleteManualVoucherCommandService
{
    private readonly ILogger<DeleteManualVoucherService> _logger;
    private readonly IRepository _repository;
    private readonly ISecurityContextProvider _securityContextProvider;

    public DeleteManualVoucherService(
        ILogger<DeleteManualVoucherService> logger,
        ISecurityContextProvider securityContextProvider,
        IRepository repository)
    {
        _logger = logger;
        _securityContextProvider = securityContextProvider;
        _repository = repository;
    }


    public async Task<CommandResponse> DeleteManualVoucherAsync(DeleteManualVoucherCommand command)
    {
        _logger.LogInformation("DeleteManualVoucherAsync process initiated. VoucherGroupItemId {GroupItemId}", command.VoucherGroupItemId);

        var commandResponse = new CommandResponse();

        try
        {
            await ProcessDeleteManualVoucher(command, commandResponse);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred during DeleteManualVoucherAsync process.");
            commandResponse.SetError(BuroErrorMessageKeys.ErrorOccurred, "An error occurred while deleting the manual voucher.");
        }

        return commandResponse;
    }

    private async Task ProcessDeleteManualVoucher(DeleteManualVoucherCommand command, CommandResponse commandResponse)
    {
        var securityContext = _securityContextProvider.GetSecurityContext();
        var currentDateTime = DateTime.UtcNow;

        var existingVouchers = _repository.GetItems<BuroVoucher>(
            c => c.VoucherGroupItemId == command.VoucherGroupItemId && c.VoucherCreationType == EnumVoucherCreationType.Manual).ToList();

        if (!existingVouchers.Any())
        {
            _logger.LogWarning("No vouchers found for group: {VoucherGroupItemId} and type: {VoucherCreationType}", command.VoucherGroupItemId, EnumVoucherCreationType.Manual);
            commandResponse.SetError(BuroErrorMessageKeys.ManualVoucherNotFound, "No vouchers found for this group");
            return;
        }

        var bulkOperations = (from voucher in existingVouchers let properties = new Dictionary<string, object> { { nameof(BuroVoucher.LastUpdateDate), currentDateTime }, { nameof(BuroVoucher.LastUpdatedBy), securityContext.UserId }, { nameof(BuroVoucher.IsMarkedToDelete), true }, } select _repository.UpdateAsync<BuroVoucher>(x => x.ItemId == voucher.ItemId, properties)).ToList();

        await Task.WhenAll(bulkOperations);

        _logger.LogInformation("Deleted {Count} vouchers in group: {VoucherGroupItemId}",
            existingVouchers.Count, command.VoucherGroupItemId);



    }
}