using Domain.Commands.Collection;
using Domain.Contracts.Collection;
using FinMongoDbRepositories;
using Infrastructure.Constants;
using Infrastructure.Contracts;
using Infrastructure.Extensions;
using Infrastructure.Models.Transaction;
using Microsoft.Extensions.Logging;
using Microsoft.OpenApi.Extensions;
using MongoDB.Driver;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Collection
{
    public class OneShotPaymentService : IOneShotPaymentService
    {
        private readonly Dictionary<EnumProductType, IAccountOneShotPaymentHandler> _paymentStrategies;
        private readonly ITransactionClientService _transactionClientService;
        private readonly ILogger<OneShotPaymentService> _logger;
        private readonly IRepository _repository;
        private readonly IFinMongoDbRepository _finMongoDbRepository;

        public OneShotPaymentService(IEnumerable<IAccountOneShotPaymentHandler> strategies,
            ITransactionClientService transactionClientService,
            ILogger<OneShotPaymentService> logger,
            IRepository repository,
            IFinMongoDbRepository finMongoDbRepository)
        {
            _paymentStrategies = strategies.ToDictionary(s => s.AccountType, s => s);
            _transactionClientService = transactionClientService;
            _logger = logger;
            _repository = repository;
            _finMongoDbRepository = finMongoDbRepository;
        }

        public async Task<CommandResponse> ProcessOneShotPaymentAsync(OneShotPaymentCommand command)
        {
            _logger.LogInformation("Processing one-shot payment for MemberItemId: {MemberItemId} with amount: {PaidAmount}", command.MemberItemId, command.PaidAmount);

            var response = new CommandResponse();

            try
            {
                var remainingAmount = command.PaidAmount;

                var (gsAccount, csAccounts, csSchedules, loanAccounts, loanSchedules) = await FetchAccountAndSchedules(command.MemberItemId);

                var transactionRequests = new List<TransactionCommand>();
                var paymentDetails = new List<OneShotPaymentDetail>();

                ApplyLoanPayments(loanSchedules, loanAccounts, ref remainingAmount, transactionRequests, paymentDetails);
                ApplyContractualSavingsPayments(csSchedules, csAccounts, ref remainingAmount, transactionRequests, paymentDetails);
                ApplyGeneralSavingPayment(gsAccount, ref remainingAmount, transactionRequests, paymentDetails);

                await InitiateOneShotPaymentTransactionAsync(command, transactionRequests);
                await ProcessPaymentStrategies(command.MemberItemId, paymentDetails);

                _logger.LogInformation("One-shot payment completed for MemberItemId: {MemberItemId}", command.MemberItemId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing one-shot payment for MemberItemId: {MemberItemId}", command.MemberItemId);

                response.SetError(BuroErrorMessageKeys.ErrorOccurred, ex.Message);
            }

            return response;
        }

        private async Task InitiateOneShotPaymentTransactionAsync(OneShotPaymentCommand command, List<TransactionCommand> transactionRequests)
        {
            var result = await _transactionClientService.InitiateTransactionAsync(new InitiateTransactionCommand
            {
                Transactions = transactionRequests,
                IsIndependentTransaction = false,
                MessageCorrelationId = command.MessageCorrelationId
            });

            if (!result.IsSuccess())
            {
                _logger.LogError("One-shot payment trnsaction failed for MemberItemId: {MemberItemId}", command.MemberItemId);

                throw new InvalidOperationException($"Transactions failed for one shot payment");
            }
        }

        private async Task<(
            BuroGeneralSavingsAccount?,
            List<BuroContractualSavingsAccount>,
            List<BuroMemberSchedule>,
            List<BuroLoanAccount>,
            List<BuroMemberSchedule>
        )> FetchAccountAndSchedules(string memberItemId)
        {
            var today = DateTime.UtcNow.Date;
            var startOfMonth = new DateTime(today.Year, today.Month, 1);
            var startOfNextMonth = startOfMonth.AddMonths(1);

            var gsAccount = await _repository.GetItemAsync<BuroGeneralSavingsAccount>(x => x.MemberItemId == memberItemId);

            var csAccounts = await _finMongoDbRepository.FindAsync<BuroContractualSavingsAccount>(x => x.MemberItemId == memberItemId);
            var csAccountItemIds = csAccounts.Select(x => x.ItemId).ToList();

            var csFilter = BuildContractualScheduleFilter(csAccountItemIds, today, startOfMonth, startOfNextMonth);
            var csSchedules = await _finMongoDbRepository.FindAsync(csFilter);

            var loanAccounts = await _finMongoDbRepository.FindAsync<BuroLoanAccount>(x => x.MemberItemId == memberItemId);
            var loanSchedules = new List<BuroMemberSchedule>();

            foreach (var account in loanAccounts)
            {
                var schedule = await _repository.GetItemAsync<BuroMemberSchedule>(
                    x => x.MemberItemId == memberItemId &&
                         x.PaymentStatus == PaymentStatus.Pending &&
                         x.ProductType == EnumProductType.Loan &&
                         x.ActualPaymentDate >= today);

                if (schedule != null)
                    loanSchedules.Add(schedule);
            }

            return (gsAccount, csAccounts, csSchedules, loanAccounts, loanSchedules);
        }

        private static FilterDefinition<BuroMemberSchedule> BuildContractualScheduleFilter(List<string> accountItemIds, DateTime today, DateTime startOfMonth, DateTime startOfNextMonth)
        {
            var builder = Builders<BuroMemberSchedule>.Filter;

            var baseFilter = builder.And(
                builder.Eq(x => x.PaymentStatus, PaymentStatus.Pending),
                builder.Eq(x => x.ProductType, EnumProductType.ContractualSaving),
                builder.In(x => x.AccountItemId, accountItemIds)
            );

            var weeklyFilter = builder.And(
                builder.Eq(x => x.PaymentInterval, EnumPaymentInterval.Weekly),
                builder.Eq(x => x.ActualPaymentDate, today)
            );

            var monthlyFilter = builder.And(
                builder.Eq(x => x.PaymentInterval, EnumPaymentInterval.Monthly),
                builder.Gte(x => x.ActualPaymentDate, startOfMonth),
                builder.Lt(x => x.ActualPaymentDate, startOfNextMonth)
            );

            return builder.And(baseFilter, builder.Or(weeklyFilter, monthlyFilter));
        }


        private void ApplyLoanPayments(List<BuroMemberSchedule> loanSchedules, List<BuroLoanAccount> loanAccounts,
            ref double remainingAmount, List<TransactionCommand> txRequests, List<OneShotPaymentDetail> paymentDetails)
        {
            foreach (var schedule in loanSchedules)
            {
                var account = loanAccounts.FirstOrDefault(a => a.ItemId == schedule.AccountItemId);
                if (account == null) continue;

                double payable = account.OverDueAmount + account.LateFeeAmount;

                if (DateTime.UtcNow.Date == schedule.OriginalPaymentDate.Date)
                {
                    payable += schedule.PayableAmount;
                }

                if (remainingAmount >= payable)
                {
                    _logger.LogInformation("Applying loan payment for AccountItemId: {AccountItemId}, Amount: {Amount}", account.ItemId, payable);

                    txRequests.Add(new TransactionCommand
                    {
                        AccountHolderNumber = account.MemberSequenceNumber,
                        AccountNumber = account.AccountSequenceNumber,
                        Amount = payable,
                        TransactionType = TransactionServiceConstrants.TransactionTypeConstants.Deposit,
                        AccountType = TransactionServiceConstrants.AccountTypeConstants.Loan,
                        Reference = EnumBuroTransactionType.LoanInstallmentRepayment.GetDisplayName(),
                    });

                    paymentDetails.Add(new OneShotPaymentDetail
                    {
                        AccountType = EnumProductType.Loan,
                        Account = account,
                        MemberSchedule = schedule,
                        RealizedAmount = payable
                    });

                    remainingAmount -= payable;
                }
            }
        }

        private void ApplyContractualSavingsPayments(List<BuroMemberSchedule> csSchedules, List<BuroContractualSavingsAccount> csAccounts,
            ref double remainingAmount, List<TransactionCommand> txRequests, List<OneShotPaymentDetail> paymentDetails)
        {
            foreach (var schedule in csSchedules)
            {
                var account = csAccounts.FirstOrDefault(a => a.ItemId == schedule.AccountItemId);
                if (account == null) continue;

                double payable = schedule.PayableAmount;

                if (remainingAmount >= payable)
                {
                    _logger.LogInformation("Applying contractual savings payment for AccountItemId: {AccountItemId}, Amount: {Amount}", account.ItemId, payable);

                    txRequests.Add(new TransactionCommand
                    {
                        AccountHolderNumber = account.MemberSequenceNumber,
                        AccountNumber = account.AccountSequenceNumber,
                        Amount = payable,
                        TransactionType = TransactionServiceConstrants.TransactionTypeConstants.Deposit,
                        AccountType = TransactionServiceConstrants.AccountTypeConstants.Current,
                        Reference = EnumBuroTransactionType.CsInstallment.GetDisplayName(),
                    });

                    paymentDetails.Add(new OneShotPaymentDetail
                    {
                        AccountType = EnumProductType.ContractualSaving,
                        Account = account,
                        MemberSchedule = schedule,
                        RealizedAmount = payable
                    });

                    remainingAmount -= payable;
                }
            }
        }

        private void ApplyGeneralSavingPayment(BuroGeneralSavingsAccount? gsAccount, ref double remainingAmount,
            List<TransactionCommand> txRequests, List<OneShotPaymentDetail> paymentDetails)
        {
            if (remainingAmount > 0 && gsAccount != null)
            {
                _logger.LogInformation("Depositing remaining amount to General Savings. AccountItemId: {AccountItemId}, Amount: {Amount}", gsAccount.ItemId, remainingAmount);

                txRequests.Add(new TransactionCommand
                {
                    AccountHolderNumber = gsAccount.MemberSequenceNumber,
                    AccountNumber = gsAccount.AccountSequenceNumber,
                    Amount = remainingAmount,
                    TransactionType = TransactionServiceConstrants.TransactionTypeConstants.Deposit,
                    AccountType = TransactionServiceConstrants.AccountTypeConstants.Savings,
                    Reference = EnumBuroTransactionType.GsDeposit.GetDisplayName(),
                });

                paymentDetails.Add(new OneShotPaymentDetail
                {
                    AccountType = EnumProductType.GeneralSaving,
                    Account = gsAccount,
                    RealizedAmount = remainingAmount
                });
            }
        }

        private async Task ProcessPaymentStrategies(string memberItemId, List<OneShotPaymentDetail> paymentDetails)
        {
            foreach (var detail in paymentDetails)
            {
                if (_paymentStrategies.TryGetValue(detail.AccountType, out var strategy))
                {
                    _logger.LogInformation("Processing strategy for AccountType: {AccountType}, AccountItemId: {AccountItemId}", detail.AccountType, detail.Account.ItemId);
                    await strategy.ProcessPaymentAsync(memberItemId, detail);
                }
            }
        }
    }
}
