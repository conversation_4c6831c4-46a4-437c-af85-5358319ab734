using Domain.Commands.Asset;
using FinMongoDbRepositories;
using Infrastructure.Constants;
using MongoDB.Driver;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Assets;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Asset;

public class UpdateAssetSubGroupService
{
    private readonly IFinMongoDbRepository _repository;

    public UpdateAssetSubGroupService(IFinMongoDbRepository repository)
    {
        _repository = repository;
    }

    public async Task<CommandResponse> UpdateAssetSubGroupAsync(UpdateAssetSubGroupCommand command)
    {
        var response = new CommandResponse();
        var assetGroup = await FetchAssetGroupAsync(command.GroupItemId);
        if (assetGroup == null)
        {
            response.SetError(BuroErrorMessageKeys.AssetGroupDoesntExists, "Asset group doesn't exist.");
            return response;
        }
        var assetSubGroup = BuildUpdateDefinition(command, assetGroup);
        await _repository.UpdateOneAsync<BuroAssetSubGroup>(x => x.ItemId == command.SubGroupItemId, assetSubGroup);
        return response;
    }

    private async Task<BuroAssetGroup?> FetchAssetGroupAsync(string groupItemId)
    {
        return await _repository.FindOneAsync<BuroAssetGroup>(item => item.ItemId == groupItemId);
    }

    private UpdateDefinition<BuroAssetSubGroup> BuildUpdateDefinition(UpdateAssetSubGroupCommand command, BuroAssetGroup assetGroup)
    {
        var update = Builders<BuroAssetSubGroup>.Update
            .Set(x => x.GroupItemId, assetGroup.ItemId)
            .Set(x => x.GroupCode, assetGroup.GroupCode)
            .Set(x => x.GroupName, assetGroup.GroupName)
            .Set(x => x.GroupDescription, assetGroup.GroupDescription)
            .Set(x => x.SubGroupCode, command.SubGroupCode)
            .Set(x => x.SubGroupName, command.SubGroupName)
            .Set(x => x.SubGroupDescription, command.SubGroupDescription)
            .Set(x => x.DepreciationRate, command.DepreciationRate)
            .Set(x => x.PartialSale, command.PartialSale)
            .Set(x => x.AdditionalProperties, command.AdditionalProperties);

        return update;
    }
}