using BuroUserPermission.Extensions;
using Domain.CommandHandlers;
using FluentValidation;
using SeliseBlocks.Genesis.Framework.Infrastructure;
using ServiceCollector.Account;
using ServiceCollector.AllowedSector;
using ServiceCollector.Approval;
using ServiceCollector.Asset;
using ServiceCollector.Authentication;
using ServiceCollector.Bank;
using ServiceCollector.BankCheque;
using ServiceCollector.Center;
using ServiceCollector.Collection;
using ServiceCollector.ConsentForm;
using ServiceCollector.CustomProperty;
using ServiceCollector.DayEnd;
using ServiceCollector.Delegation;
using ServiceCollector.Dms;
using ServiceCollector.Employee;
using ServiceCollector.ExternalSync;
using ServiceCollector.Helper;
using ServiceCollector.Holiday;
using ServiceCollector.Infrastructure;
using ServiceCollector.Inventory;
using ServiceCollector.Logger;
using ServiceCollector.LoginActivity;
using ServiceCollector.Member;
using ServiceCollector.Mfcib;
using ServiceCollector.Notice;
using ServiceCollector.Office;
using ServiceCollector.Register;
using ServiceCollector.Role;
using ServiceCollector.Shared;
using ServiceCollector.Tasks;
using ServiceCollector.Transaction;
using ServiceCollector.Users;
using ServiceCollector.Vendor;
using ServiceCollector.Voucher;
using Validators.Employee;

namespace WebService;

public static class Program
{
    private static async Task Main(string[] args)
    {
        var blocksWebApiPipelineBuilderOptions = new BlocksWebApiPipelineBuilderOptions
        {
            UseFileLogging = true,
            UseTracingLogging = true,
            CommandLineArguments = args,
            UseAuditLoggerMiddleware = true,
            AddApplicationServices = AddApplicationServices,
            UseJwtBearerAuthentication = true,
            UserUrlsInDevelopment = UserUrlsInDevelopment,
            AddRequiredQueues = AddRequiredQueues
        };

        var pipeline = await BlocksWebApiPipelineBuilder.BuildBlocksWebApiPipeline(blocksWebApiPipelineBuilderOptions);

        pipeline.Build().Run();
    }

    private static string[] UserUrlsInDevelopment(IAppSettings appSettings)
    {
        return new[] { "http://burobd.seliselocal.com/" };
    }

    private static IEnumerable<string> AddRequiredQueues(IAppSettings appSettings)
    {
        return new[]
        {
            appSettings.BlocksAuditLogQueueName
        };
    }

    private static void AddApplicationServices(
        IServiceCollection serviceCollection,
        IAppSettings appSettings)
    {
        serviceCollection.RegisterSingleton<ICommandSender, CommandSender>();
        serviceCollection.RegisterCollection(typeof(ICommandHandler<,>), new[]
        {
            typeof(CreateOfficeCommandHandler).Assembly
        });

        serviceCollection.RegisterCollection(typeof(IValidator<>), new[]
        {
            typeof(AddEmployeeCommandValidator).Assembly
        });

        serviceCollection.RegisterAuthorizationService();

        serviceCollection.AddInfrastructureService(appSettings);
        serviceCollection.AddOfficeService(appSettings);
        serviceCollection.AddAuthenticationService(appSettings);
        serviceCollection.AddEmployeeServices(appSettings);
        serviceCollection.AddHolidayService(appSettings);
        serviceCollection.AddCustomPropertyServices(appSettings);
        serviceCollection.AddUserServices(appSettings);
        serviceCollection.AddApprovalServices(appSettings);
        serviceCollection.AddMemberServices(appSettings);
        serviceCollection.AddRoleServices(appSettings);
        serviceCollection.AddCenterServices(appSettings);
        serviceCollection.AddAccountServices(appSettings);
        serviceCollection.AddLoggerServices(appSettings);
        serviceCollection.AddDelegationServices(appSettings);
        serviceCollection.AddLoginActivityServices(appSettings);
        serviceCollection.AddTasksServices(appSettings);
        serviceCollection.AddDmsService(appSettings);
        serviceCollection.AddInventoryServices(appSettings);
        serviceCollection.AddHelperService(appSettings);
        serviceCollection.AddExternalSyncService(appSettings);
        serviceCollection.AddDayEndService(appSettings);
        serviceCollection.AddMfcibServices(appSettings);
        serviceCollection.AddVoucherConfigurationService(appSettings);
        serviceCollection.AddSharedServices(appSettings);
        serviceCollection.AddManualVoucherService(appSettings);
        serviceCollection.AddTransactionServices(appSettings);
        serviceCollection.AddCollectionServices();
        serviceCollection.AddConsentFormServices(appSettings);
        serviceCollection.AddNoticeService(appSettings);
        serviceCollection.AddAssetServices(appSettings);
        serviceCollection.AddVendorServices(appSettings);
        serviceCollection.AddAllowedSectorServices(appSettings);
        serviceCollection.AddBankServices(appSettings);
        serviceCollection.AddBankChequeServices(appSettings);
        serviceCollection.AddRegisterServices();

        serviceCollection.AddApiDocument(new BlocksSwaggerOptions
        {
            Description = "Buro web service",
            BlocksSwaggerInstances = new List<BlocksSwaggerInstance>
            {
                new()
                {
                    Environment = "dev-az"
                },
                new()
                {
                    Environment = "stg-az"
                }
            }
        }, appSettings);
    }
}